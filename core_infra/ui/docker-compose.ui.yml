version: '3.8'

services:
  documentation-portal:
    build:
      context: ./documentation_portal
      dockerfile: Dockerfile
    container_name: regulens-docs-portal
    ports:
      - "${DOCS_PORTAL_PORT:-8501}:${DOCS_PORTAL_PORT:-8501}"
    environment:
      - STREAMLIT_SERVER_PORT=${DOCS_PORTAL_PORT:-8501}
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - API_BASE_URL=http://api:${API_PORT:-8000}
    volumes:
      - ./documentation_portal:/app
      - ../database/schema.sql:/app/schema.sql:ro
      - ../.env:/app/.env:ro
    networks:
      - regulens-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  testing-portal:
    build:
      context: ./testing_portal
      dockerfile: Dockerfile
    container_name: regulens-testing-portal
    ports:
      - "${TESTING_PORTAL_PORT:-8502}:${TESTING_PORTAL_PORT:-8502}"
    environment:
      - STREAMLIT_SERVER_PORT=${TESTING_PORTAL_PORT:-8502}
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      - API_BASE_URL=http://api:${API_PORT:-8000}
      - TESTING_MODE=true
    volumes:
      - ./testing_portal:/app
      - ../.env:/app/.env:ro
    networks:
      - regulens-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8502"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  api:
    build:
      context: ../
      dockerfile: Dockerfile
    container_name: regulens-api
    ports:
      - "${API_PORT:-8000}:${API_PORT:-8000}"
    environment:
      - PYTHONPATH=/app
      - API_HOST=0.0.0.0
      - API_PORT=${API_PORT:-8000}
    env_file:
      - ../.env
    volumes:
      - ../:/app
    networks:
      - regulens-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

networks:
  regulens-network:
    name: regulens-network
    external: true 