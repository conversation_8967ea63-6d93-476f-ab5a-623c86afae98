"""
Risk Scoring Service

Provides comprehensive risk scoring capabilities for customers and transactions
using machine learning models and rule-based engines.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
import json

import numpy as np
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib

from core_infra.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class RiskScoringService:
    """
    Enterprise-grade risk scoring service for financial compliance.
    
    Provides:
    - Customer risk scoring with multiple risk dimensions
    - Transaction risk scoring for fraud and AML detection
    - Model training, validation, and deployment
    - Real-time and batch scoring capabilities
    - Model drift detection and auto-retraining
    """
    
    def __init__(self, supabase_client, vector_store=None):
        """Initialize the risk scoring service."""
        self.supabase = supabase_client
        self.vector_store = vector_store
        self.models = {}
        self.scalers = {}
        self.feature_columns = {}
        self.model_metadata = {}
        
        # Load existing models if any
        self._load_models()
    
    async def score_customer_risk(
        self,
        customer_id: str,
        tenant_id: str,
        model_name: Optional[str] = None,
        features: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive risk score for a customer.
        
        Args:
            customer_id: Customer UUID
            tenant_id: Tenant UUID for multi-tenant isolation
            model_name: Specific model to use (optional)
            features: Pre-calculated features (optional)
            
        Returns:
            Risk scoring results with breakdown and explanation
        """
        try:
            logger.info(f"Calculating customer risk score for {customer_id}")
            
            # Get customer data
            customer = await self._get_customer_data(customer_id, tenant_id)
            if not customer:
                raise ValueError(f"Customer {customer_id} not found")
            
            # Extract or calculate features
            if not features:
                features = await self._extract_customer_features(customer, tenant_id)
            
            # Get appropriate model
            model_name = model_name or 'customer_risk_default'
            model_info = await self._get_risk_model(model_name, 'customer_risk')
            
            if not model_info:
                # Use rule-based scoring if no ML model available
                return await self._rule_based_customer_scoring(customer, features, tenant_id)
            
            # Prepare features for scoring
            feature_vector = self._prepare_features(features, model_info['feature_definitions'])
            
            # Calculate risk scores
            overall_score = self._calculate_risk_score(feature_vector, model_info)
            
            # Calculate component scores
            component_scores = await self._calculate_component_scores(
                customer, features, tenant_id
            )
            
            # Determine risk grade and category
            risk_grade = self._determine_risk_grade(overall_score)
            risk_category = self._determine_risk_category(overall_score)
            
            # Generate explanation
            explanation = self._generate_score_explanation(
                features, component_scores, overall_score
            )
            
            # Check for alerts
            alerts = self._check_risk_alerts(overall_score, component_scores, customer)
            
            # Store the risk score
            risk_score_data = {
                'tenant_id': tenant_id,
                'customer_id': customer_id,
                'model_id': model_info['id'],
                'overall_risk_score': float(overall_score),
                'credit_risk_score': float(component_scores.get('credit_risk', 0)),
                'fraud_risk_score': float(component_scores.get('fraud_risk', 0)),
                'aml_risk_score': float(component_scores.get('aml_risk', 0)),
                'operational_risk_score': float(component_scores.get('operational_risk', 0)),
                'risk_grade': risk_grade,
                'risk_category': risk_category,
                'contributing_factors': json.dumps(features),
                'risk_indicators': json.dumps(component_scores),
                'confidence_interval': json.dumps({
                    'lower': float(overall_score * 0.9),
                    'upper': float(overall_score * 1.1)
                }),
                'probability_of_default': float(self._calculate_probability_of_default(overall_score)),
                'expected_loss': float(self._calculate_expected_loss(overall_score, customer)),
                'score_explanation': explanation,
                'alerts_generated': json.dumps(alerts),
                'review_required': overall_score > settings.CUSTOMER_RISK_REVIEW_TRIGGER_THRESHOLD,
                'score_date': datetime.utcnow().isoformat(),
                'expiry_date': (datetime.utcnow() + timedelta(hours=settings.CUSTOMER_RISK_SCORE_CACHE_TTL_HOURS)).isoformat()
            }
            
            # Store in database
            result = self.supabase.table('customer_risk_scores').insert(risk_score_data).execute()
            
            logger.info(f"Customer risk score calculated: {overall_score} ({risk_category})")
            
            return {
                'customer_id': customer_id,
                'overall_risk_score': overall_score,
                'risk_grade': risk_grade,
                'risk_category': risk_category,
                'component_scores': component_scores,
                'explanation': explanation,
                'alerts': alerts,
                'review_required': risk_score_data['review_required'],
                'model_used': model_name,
                'score_id': result.data[0]['id']
            }
            
        except Exception as e:
            logger.error(f"Error calculating customer risk score: {str(e)}")
            raise
    
    async def score_transaction_risk(
        self,
        transaction_id: str,
        tenant_id: str,
        real_time: bool = True
    ) -> Dict[str, Any]:
        """
        Calculate risk score for a transaction in real-time or batch.
        
        Args:
            transaction_id: Transaction UUID
            tenant_id: Tenant UUID
            real_time: Whether this is real-time scoring
            
        Returns:
            Transaction risk scoring results
        """
        try:
            logger.info(f"Calculating transaction risk score for {transaction_id}")
            
            # Get transaction data
            transaction = await self._get_transaction_data(transaction_id, tenant_id)
            if not transaction:
                raise ValueError(f"Transaction {transaction_id} not found")
            
            # Extract features
            features = await self._extract_transaction_features(transaction, tenant_id)
            
            # Get model
            model_info = await self._get_risk_model('transaction_risk_default', 'fraud_risk')
            
            if not model_info:
                # Use rule-based scoring
                return await self._rule_based_transaction_scoring(transaction, features, tenant_id)
            
            # Calculate component scores
            fraud_score = self._calculate_fraud_score(features, model_info)
            aml_score = self._calculate_aml_score(features, model_info)
            sanctions_score = self._calculate_sanctions_score(features, transaction)
            pep_score = self._calculate_pep_score(features, transaction)
            behavioral_score = self._calculate_behavioral_score(features, transaction, tenant_id)
            velocity_score = self._calculate_velocity_score(features, transaction, tenant_id)
            geographic_score = self._calculate_geographic_score(features, transaction)
            
            # Calculate overall risk score
            overall_risk_score = np.mean([
                fraud_score, aml_score, behavioral_score, velocity_score, geographic_score
            ])
            
            # Determine risk level
            risk_level = self._determine_transaction_risk_level(overall_risk_score)
            
            # Extract risk factors
            risk_factors = self._identify_risk_factors(features, transaction)
            
            # Detect anomalies
            anomaly_indicators = self._detect_anomalies(features, transaction, tenant_id)
            
            # Auto decision logic
            auto_decision = self._make_auto_decision(overall_risk_score, risk_factors)
            
            # Generate explanation
            explanation = self._generate_transaction_explanation(
                features, {
                    'fraud_score': fraud_score,
                    'aml_score': aml_score,
                    'behavioral_score': behavioral_score,
                    'velocity_score': velocity_score
                }
            )
            
            # Store transaction risk score
            risk_score_data = {
                'tenant_id': tenant_id,
                'transaction_id': transaction_id,
                'model_id': model_info['id'],
                'fraud_score': float(fraud_score),
                'aml_score': float(aml_score),
                'sanctions_score': float(sanctions_score) if sanctions_score else None,
                'pep_score': float(pep_score) if pep_score else None,
                'behavioral_score': float(behavioral_score),
                'velocity_score': float(velocity_score),
                'geographic_score': float(geographic_score),
                'overall_risk_score': float(overall_risk_score),
                'risk_level': risk_level,
                'risk_factors': json.dumps(risk_factors),
                'anomaly_indicators': json.dumps(anomaly_indicators),
                'pattern_matches': json.dumps(self._match_patterns(features)),  # Implement _match_patterns
                'false_positive_probability': float(self._calculate_false_positive_probability(overall_risk_score)),
                'investigation_priority': int(self._calculate_investigation_priority(overall_risk_score, risk_factors)),
                'auto_decision': auto_decision,
                'score_explanation': explanation
            }
            
            # Store in database
            result = self.supabase.table('transaction_risk_scores').insert(risk_score_data).execute()
            
            logger.info(f"Transaction risk score calculated: {overall_risk_score} ({risk_level})")
            
            return {
                'transaction_id': transaction_id,
                'overall_risk_score': overall_risk_score,
                'risk_level': risk_level,
                'component_scores': {
                    'fraud_score': fraud_score,
                    'aml_score': aml_score,
                    'behavioral_score': behavioral_score,
                    'velocity_score': velocity_score
                },
                'auto_decision': auto_decision,
                'investigation_priority': risk_score_data['investigation_priority'],
                'explanation': explanation,
                'score_id': result.data[0]['id']
            }
            
        except Exception as e:
            logger.error(f"Error calculating transaction risk score: {str(e)}")
            raise
    
    async def train_risk_model(
        self,
        model_name: str,
        model_type: str,
        tenant_id: str,
        training_data: Optional[Dict] = None,
        algorithm: str = 'random_forest',
        user_id: str = None
    ) -> Dict[str, Any]:
        """
        Train a new risk scoring model.
        
        Args:
            model_name: Name for the model
            model_type: Type of risk model (customer_risk, fraud_risk, etc.)
            tenant_id: Tenant UUID
            training_data: Pre-prepared training data (optional)
            algorithm: ML algorithm to use
            
        Returns:
            Model training results and metadata
        """
        try:
            logger.info(f"Training risk model: {model_name} ({model_type})")
            
            # Prepare training data
            if not training_data:
                training_data = await self._prepare_training_data(model_type, tenant_id)
            
            X_train, X_test, y_train, y_test = train_test_split(
                training_data['features'], 
                training_data['targets'], 
                test_size=0.2, 
                random_state=42,
                stratify=training_data['targets']
            )
            
            # Feature scaling
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model based on algorithm
            if algorithm == 'random_forest':
                model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
            elif algorithm == 'logistic_regression':
                model = LogisticRegression(
                    random_state=42,
                    max_iter=1000
                )
            else:
                raise ValueError(f"Unsupported algorithm: {algorithm}")
            
            # Train the model
            model.fit(X_train_scaled, y_train)
            
            # Validate model
            y_pred = model.predict(X_test_scaled)
            y_pred_proba = model.predict_proba(X_test_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # Calculate metrics
            metrics = {
                'accuracy': float(accuracy_score(y_test, y_pred)),
                'precision': float(precision_score(y_test, y_pred, average='weighted')),
                'recall': float(recall_score(y_test, y_pred, average='weighted')),
                'f1_score': float(f1_score(y_test, y_pred, average='weighted'))
            }
            
            # Feature importance
            feature_importance = {}
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(
                    training_data['feature_names'],
                    model.feature_importances_.tolist()
                ))
            
            # Save model artifacts
            model_artifacts = {
                'model': model,
                'scaler': scaler,
                'feature_names': training_data['feature_names'],
                'metrics': metrics,
                'feature_importance': feature_importance
            }
            
            # Save model to database
            model_data = {
                'tenant_id': tenant_id,
                'model_name': model_name,
                'model_type': model_type,
                'model_version': '1.0',
                'algorithm_type': algorithm,
                'model_parameters': json.dumps(model.get_params()),
                'feature_definitions': json.dumps(training_data['feature_names']),
                'training_data_metadata': json.dumps({
                    'training_samples': len(X_train),
                    'test_samples': len(X_test),
                    'features_count': len(training_data['feature_names'])
                }),
                'performance_metrics': json.dumps(metrics),
                'validation_results': json.dumps(metrics),
                'model_status': 'training',
                'accuracy_threshold': settings.RISK_MODEL_ACCURACY_THRESHOLD,
                'current_accuracy': metrics['accuracy'],
                'model_drift_score': 0.0,
                'created_by': user_id if user_id else tenant_id
            }
            
            # Insert model record
            result = self.supabase.table('risk_scoring_models').insert(model_data).execute()
            model_id = result.data[0]['id']
            
            # Save model artifacts (in production, save to cloud storage)
            self.models[model_name] = model_artifacts
            
            logger.info(f"Risk model trained successfully: {model_name}")
            
            return {
                'model_id': model_id,
                'model_name': model_name,
                'metrics': metrics,
                'feature_importance': feature_importance,
                'status': 'trained'
            }
            
        except Exception as e:
            logger.error(f"Error training risk model: {str(e)}")
            raise
    
    # Private helper methods
    
    async def _get_customer_data(self, customer_id: str, tenant_id: str) -> Optional[Dict]:
        """Get customer data from database."""
        result = self.supabase.table('customers').select('*').eq('id', customer_id).eq('tenant_id', tenant_id).execute()
        return result.data[0] if result.data else None
    
    async def _get_transaction_data(self, transaction_id: str, tenant_id: str) -> Optional[Dict]:
        """Get transaction data from database."""
        result = self.supabase.table('transactions').select('*').eq('id', transaction_id).eq('tenant_id', tenant_id).execute()
        return result.data[0] if result.data else None
    
    async def _extract_customer_features(self, customer: Dict, tenant_id: str) -> Dict[str, Any]:
        """Extract features for customer risk scoring."""
        # Get transaction history
        transactions = self.supabase.table('transactions').select('*').eq('customer_id', customer['id']).execute()
        transaction_data = transactions.data if transactions.data else []
        
        # Calculate features
        features = {
            'customer_age_days': (datetime.utcnow() - datetime.fromisoformat(customer['onboarding_date'].replace('Z', '+00:00'))).days,
            'kyc_status_score': {'verified': 10, 'pending': 5, 'rejected': 0, 'expired': 2}.get(customer['kyc_status'], 0),
            'pep_status': int(customer['pep_status']),
            'transaction_count_30d': len([t for t in transaction_data if (datetime.utcnow() - datetime.fromisoformat(t['transaction_date'].replace('Z', '+00:00'))).days <= 30]),
            'transaction_volume_30d': sum([float(t['amount']) for t in transaction_data if (datetime.utcnow() - datetime.fromisoformat(t['transaction_date'].replace('Z', '+00:00'))).days <= 30]),
            'avg_transaction_amount': np.mean([float(t['amount']) for t in transaction_data]) if transaction_data else 0,
            'max_transaction_amount': max([float(t['amount']) for t in transaction_data]) if transaction_data else 0,
            'unique_countries': len(set([t.get('country_of_destination', 'unknown') for t in transaction_data])),
            'sanctions_check_score': {'clear': 10, 'pending': 5, 'match': 0}.get(customer['sanctions_check_status'], 5),
            'risk_rating_numeric': {'low': 25, 'medium': 50, 'high': 75}.get(customer['risk_rating'], 50)
        }
        
        return features
    
    async def _extract_transaction_features(self, transaction: Dict, tenant_id: str) -> Dict[str, Any]:
        """Extract features for transaction risk scoring."""
        # Get customer data
        customer = await self._get_customer_data(transaction['customer_id'], tenant_id)
        
        # Get customer's recent transactions for behavioral analysis
        recent_txns = self.supabase.table('transactions').select('*').eq('customer_id', transaction['customer_id']).gte('transaction_date', (datetime.utcnow() - timedelta(days=30)).isoformat()).execute()
        recent_data = recent_txns.data if recent_txns.data else []
        
        features = {
            'amount': float(transaction['amount']),
            'amount_log': np.log(max(float(transaction['amount']), 1)),
            'hour_of_day': datetime.fromisoformat(transaction['transaction_date'].replace('Z', '+00:00')).hour,
            'day_of_week': datetime.fromisoformat(transaction['transaction_date'].replace('Z', '+00:00')).weekday(),
            'customer_age_days': (datetime.utcnow() - datetime.fromisoformat(customer['onboarding_date'].replace('Z', '+00:00'))).days if customer else 0,
            'customer_risk_rating': {'low': 25, 'medium': 50, 'high': 75}.get(customer['risk_rating'], 50) if customer else 50,
            'recent_transaction_count': len(recent_data),
            'recent_transaction_volume': sum([float(t['amount']) for t in recent_data]),
            'avg_recent_amount': np.mean([float(t['amount']) for t in recent_data]) if recent_data else 0,
            'is_weekend': datetime.fromisoformat(transaction['transaction_date'].replace('Z', '+00:00')).weekday() >= 5,
            'is_night_time': datetime.fromisoformat(transaction['transaction_date'].replace('Z', '+00:00')).hour < 6 or datetime.fromisoformat(transaction['transaction_date'].replace('Z', '+00:00')).hour > 22,
            'transaction_type_numeric': {'deposit': 1, 'withdrawal': 2, 'transfer': 3, 'payment': 4}.get(transaction['transaction_type'], 0),
            'cross_border': 1 if transaction.get('country_of_origin') != transaction.get('country_of_destination') else 0
        }
        
        return features
    
    def _determine_risk_grade(self, score: float) -> str:
        """Determine risk grade based on score."""
        if score >= 90: return 'D'
        elif score >= 80: return 'C'
        elif score >= 70: return 'CCC'
        elif score >= 60: return 'B'
        elif score >= 50: return 'BB'
        elif score >= 40: return 'BBB'
        elif score >= 30: return 'A'
        elif score >= 20: return 'AA'
        else: return 'AAA'
    
    def _determine_risk_category(self, score: float) -> str:
        """Determine risk category based on score."""
        if score >= 75: return 'critical'
        elif score >= 50: return 'high'
        elif score >= 25: return 'medium'
        else: return 'low'
    
    def _determine_transaction_risk_level(self, score: float) -> str:
        """Determine transaction risk level."""
        return self._determine_risk_category(score)
    
    def _generate_score_explanation(self, features: Dict, component_scores: Dict, overall_score: float) -> str:
        """Generate human-readable explanation for risk score."""
        explanations = []
        
        if overall_score > 75:
            explanations.append("High risk profile due to multiple risk factors")
        elif overall_score > 50:
            explanations.append("Moderate risk profile requiring enhanced monitoring")
        else:
            explanations.append("Low risk profile with standard monitoring")
        
        # Add specific factor explanations
        if features.get('pep_status'):
            explanations.append("Customer is a Politically Exposed Person (PEP)")
        
        if component_scores.get('fraud_risk', 0) > 70:
            explanations.append("Elevated fraud risk indicators detected")
        
        if features.get('transaction_volume_30d', 0) > 100000:
            explanations.append("High transaction volume in recent period")
        
        return ". ".join(explanations)
    
    def _generate_transaction_explanation(self, features: Dict, scores: Dict) -> str:
        """Generate explanation for transaction risk score."""
        explanations = []
        
        if features.get('amount', 0) > 50000:
            explanations.append("Large transaction amount")
        
        if features.get('is_weekend'):
            explanations.append("Transaction occurring on weekend")
        
        if features.get('is_night_time'):
            explanations.append("Transaction occurring outside business hours")
        
        if features.get('cross_border'):
            explanations.append("Cross-border transaction")
        
        if scores.get('behavioral_score', 0) > 70:
            explanations.append("Unusual transaction pattern detected")
        
        return ". ".join(explanations) if explanations else "Standard transaction profile"
    
    # Additional helper methods would be implemented here...
    # Including model loading, rule-based scoring, component calculations, etc.
    
    def _load_models(self):
        """Load existing models from storage."""
        # Example implementation: load from Supabase or cloud storage
        self.models = {}  # Dictionary to hold loaded models
        # In production, implement actual loading logic here
        try:
            # Assuming a model registry service
            model_data = self.supabase.table('risk_models').select('*').execute()
            for model in model_data.data:
                self.models[model['name']] = pickle.loads(model['model_data'])  # Or use joblib.load
        except Exception as e:
            logger.error(f"Failed to load models: {str(e)}")
            raise SystemException("Model loading failed")
    
    async def _get_risk_model(self, model_name: str, model_type: str) -> Optional[Dict]:
        """Get risk model metadata from database."""
        result = self.supabase.table('risk_scoring_models').select('*').eq('model_name', model_name).eq('model_type', model_type).eq('model_status', 'production').execute()
        return result.data[0] if result.data else None
    
    def _prepare_features(self, features: Dict, feature_definitions: List[str]) -> np.ndarray:
        """Prepare feature vector for model input."""
        return np.array([features.get(feat, 0) for feat in feature_definitions]).reshape(1, -1)
    
    def _calculate_risk_score(self, feature_vector: np.ndarray, model_info: Dict) -> float:
        """Calculate risk score using ML model."""
        # In production, load actual model and predict
        # For now, return a mock score
        return float(np.random.uniform(20, 80))
    
    async def _calculate_component_scores(self, customer: Dict, features: Dict, tenant_id: str) -> Dict[str, float]:
        """Calculate individual risk component scores."""
        return {
            'credit_risk': float(np.random.uniform(20, 60)),
            'fraud_risk': float(np.random.uniform(10, 70)),
            'aml_risk': float(np.random.uniform(15, 65)),
            'operational_risk': float(np.random.uniform(10, 50))
        }
    
    def _check_risk_alerts(self, overall_score: float, component_scores: Dict, customer: Dict) -> List[Dict]:
        """Check if risk score triggers any alerts."""
        alerts = []
        
        if overall_score > settings.CUSTOMER_RISK_ALERT_THRESHOLD:
            alerts.append({
                'type': 'high_risk_score',
                'severity': 'high',
                'message': f'Customer risk score ({overall_score}) exceeds threshold'
            })
        
        return alerts
    
    def _calculate_probability_of_default(self, risk_score: float) -> float:
        """Calculate probability of default based on risk score."""
        # Simple linear mapping - in production use calibrated models
        return min(risk_score / 100.0, 0.99)
    
    def _calculate_expected_loss(self, risk_score: float, customer: Dict) -> float:
        """Calculate expected loss for customer."""
        # Simplified calculation - in production use sophisticated models
        pod = self._calculate_probability_of_default(risk_score)
        exposure = 100000  # Default exposure
        lgd = 0.6  # Loss given default
        return pod * exposure * lgd
    
    # More helper methods for transaction scoring...
    
    def _calculate_fraud_score(self, features: Dict, model_info: Dict) -> float:
        """Calculate fraud risk score for transaction."""
        return float(np.random.uniform(10, 80))
    
    def _calculate_aml_score(self, features: Dict, model_info: Dict) -> float:
        """Calculate AML risk score for transaction."""
        return float(np.random.uniform(15, 75))
    
    def _calculate_sanctions_score(self, features: Dict, transaction: Dict) -> Optional[float]:
        """Calculate sanctions screening score."""
        return float(np.random.uniform(5, 30)) if np.random.random() > 0.8 else None
    
    def _calculate_pep_score(self, features: Dict, transaction: Dict) -> Optional[float]:
        """Calculate PEP risk score."""
        return float(np.random.uniform(10, 60)) if features.get('customer_pep_status') else None
    
    def _calculate_behavioral_score(self, features: Dict, transaction: Dict, tenant_id: str) -> float:
        """Calculate behavioral anomaly score."""
        return float(np.random.uniform(5, 70))
    
    def _calculate_velocity_score(self, features: Dict, transaction: Dict, tenant_id: str) -> float:
        """Calculate velocity/frequency risk score."""
        return float(np.random.uniform(10, 65))
    
    def _calculate_geographic_score(self, features: Dict, transaction: Dict) -> float:
        """Calculate geographic risk score."""
        return float(np.random.uniform(5, 50))
    
    def _identify_risk_factors(self, features: Dict, transaction: Dict) -> List[str]:
        """Identify specific risk factors for transaction."""
        factors = []
        
        if features.get('amount', 0) > 50000:
            factors.append('high_amount')
        
        if features.get('cross_border'):
            factors.append('cross_border')
        
        if features.get('is_night_time'):
            factors.append('unusual_timing')
        
        return factors
    
    def _detect_anomalies(self, features: Dict, transaction: Dict, tenant_id: str) -> Dict[str, Any]:
        """Detect anomalies in transaction."""
        # Amount anomaly detection
        amount = features.get('amount', 0)
        avg_amount = features.get('avg_transaction_amount', 0)
        amount_anomaly = False
        if avg_amount > 0:
            amount_anomaly = amount > (avg_amount * 3)  # 3x average is anomalous
        else:
            amount_anomaly = amount > 100000
        
        # Frequency anomaly detection
        frequency_anomaly = self._detect_frequency_anomaly(features, transaction)
        
        return {
            'amount_anomaly': amount_anomaly,
            'timing_anomaly': features.get('is_night_time', False),
            'frequency_anomaly': frequency_anomaly
        }
    
    def _detect_frequency_anomaly(self, features: Dict, transaction: Dict) -> bool:
        """Detect frequency-based anomalies in transaction patterns."""
        # Check for sudden burst of transactions
        tx_count_1h = features.get('transaction_count_1h', 0)
        tx_count_24h = features.get('transaction_count_24h', 0)
        tx_count_7d = features.get('transaction_count_7d', 0)
        
        # Calculate expected hourly rate based on historical data
        if tx_count_7d > 0:
            avg_hourly_rate_7d = tx_count_7d / (7 * 24)
            
            # Detect if current hour has significantly more transactions
            if tx_count_1h > max(5, avg_hourly_rate_7d * 5):  # 5x normal rate
                return True
        
        # Check for rapid consecutive transactions
        if tx_count_1h > 10:  # More than 10 transactions in an hour
            return True
        
        # Check daily pattern anomaly
        if tx_count_24h > 0 and tx_count_7d > 0:
            avg_daily_rate = tx_count_7d / 7
            if tx_count_24h > max(20, avg_daily_rate * 3):  # 3x daily average
                return True
        
        # Check for velocity changes
        velocity_change = features.get('velocity_change', 0)
        if abs(velocity_change) > 200:  # 200% change in transaction velocity
            return True
        
        return False
    
    def _make_auto_decision(self, overall_score: float, risk_factors: List[str]) -> str:
        """Make automated decision based on risk score."""
        if overall_score > 80 or 'high_amount' in risk_factors:
            return 'review'
        elif overall_score > 60:
            return 'escalate'
        elif overall_score > 40:
            return 'approve'
        else:
            return 'approve'
    
    def _calculate_false_positive_probability(self, score: float) -> float:
        """Calculate probability that this is a false positive."""
        return max(0.1, 1.0 - (score / 100.0))
    
    def _calculate_investigation_priority(self, score: float, risk_factors: List[str]) -> int:
        """Calculate investigation priority (1-10)."""
        base_priority = int(score / 10)
        priority_boost = len(risk_factors)
        return min(10, base_priority + priority_boost)
    
    async def _rule_based_customer_scoring(self, customer: Dict, features: Dict, tenant_id: str) -> Dict[str, Any]:
        """Fallback rule-based customer scoring when no ML model available."""
        # Implement rule-based scoring logic
        base_score = features.get('risk_rating_numeric', 50)
        
        return {
            'customer_id': customer['id'],
            'overall_risk_score': base_score,
            'risk_grade': self._determine_risk_grade(base_score),
            'risk_category': self._determine_risk_category(base_score),
            'component_scores': await self._calculate_component_scores(customer, features, tenant_id),
            'explanation': 'Rule-based scoring (no ML model available)',
            'alerts': [],
            'review_required': base_score > 75,
            'model_used': 'rule_based'
        }
    
    async def _rule_based_transaction_scoring(self, transaction: Dict, features: Dict, tenant_id: str) -> Dict[str, Any]:
        """Fallback rule-based transaction scoring."""
        # Simple rule-based logic
        score = 20  # Base score
        
        if features.get('amount', 0) > 50000:
            score += 30
        if features.get('cross_border'):
            score += 20
        if features.get('is_night_time'):
            score += 15
        if features.get('is_weekend'):
            score += 10
        
        return {
            'transaction_id': transaction['id'],
            'overall_risk_score': min(score, 100),
            'risk_level': self._determine_transaction_risk_level(score),
            'component_scores': {
                'fraud_score': score * 0.8,
                'aml_score': score * 0.6,
                'behavioral_score': score * 0.4,
                'velocity_score': score * 0.5
            },
            'auto_decision': self._make_auto_decision(score, []),
            'investigation_priority': self._calculate_investigation_priority(score, []),
            'explanation': 'Rule-based scoring (no ML model available)'
        }
    
    async def _prepare_training_data(self, model_type: str, tenant_id: str) -> Dict[str, Any]:
        """Prepare training data for model training."""
        # In production, implement sophisticated data preparation
        # For now, return mock training data
        n_samples = 1000
        n_features = 10
        
        return {
            'features': np.random.randn(n_samples, n_features),
            'targets': np.random.randint(0, 2, n_samples),
            'feature_names': [f'feature_{i}' for i in range(n_features)]
        } 

    def _match_patterns(self, features: Dict) -> List[str]:
        """
        Implement pattern matching logic to identify specific patterns in features.
        This is a placeholder and would require actual pattern definition and logic.
        """
        patterns = []
        # Example: If a customer has a high transaction volume and a high risk rating,
        # it might indicate a high-risk customer.
        if features.get('transaction_volume_30d', 0) > 100000 and features.get('risk_rating_numeric', 50) > 70:
            patterns.append('high_transaction_volume_with_high_risk_rating')
        # If a customer has a large transaction amount and a high risk rating,
        # it might indicate a high-risk customer.
        if features.get('amount', 0) > 50000 and features.get('risk_rating_numeric', 50) > 70:
            patterns.append('large_amount_with_high_risk_rating')
        return patterns