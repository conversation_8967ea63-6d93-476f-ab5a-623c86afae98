"""Form Extractor for Computer Vision Service

This module provides intelligent form field extraction and data capture
from various document types including forms, applications, and structured documents.
"""

import base64
import logging
import re
from typing import Dict, List, Any, Optional
import numpy as np
from PIL import Image
import io
from datetime import datetime
from enum import Enum

from core_infra.config import settings
from core_infra.exceptions import DataValidationException
from core_infra.monitoring.observability import monitor_performance

logger = logging.getLogger(__name__)


class FieldType(Enum):
    """Types of form fields that can be extracted."""
    TEXT = "text"
    CHECKBOX = "checkbox"
    SIGNATURE = "signature"
    DATE = "date"
    NUMBER = "number"
    EMAIL = "email"
    PHONE = "phone"
    ADDRESS = "address"
    TABLE = "table"


class FormExtractor:
    """
    Intelligent form extraction service using computer vision.
    
    Features:
    - Automatic form layout detection
    - Field type identification
    - Value extraction with validation
    - Table detection and extraction
    """
    
    def __init__(self):
        """Initialize form extractor with configuration."""
        self.field_patterns = {
            FieldType.EMAIL: r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            FieldType.PHONE: r'[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{4,6}',
            FieldType.DATE: r'\b(?:\d{1,2}[-/]\d{1,2}[-/]\d{2,4}|\d{4}[-/]\d{1,2}[-/]\d{1,2})\b',
            FieldType.NUMBER: r'\b\d+(?:\.\d+)?\b'
        }
        logger.info("Form extractor initialized")
    
    @monitor_performance
    async def extract_form_fields(
        self,
        image_data: bytes,
        form_type: Optional[str] = None,
        language: str = 'en'
    ) -> Dict[str, Any]:
        """
        Extract fields from a form image.
        
        Args:
            image_data: Form image data
            form_type: Expected form type (optional)
            language: Language of the form
            
        Returns:
            Extracted form data with fields and metadata
        """
        try:
            # Decode image
            image = self._decode_image(image_data)
            
            # Detect form type
            detected_form_type = form_type or self._detect_form_type(image)
            
            # Extract fields
            fields = await self._extract_fields(image, detected_form_type)
            
            # Extract tables
            tables = await self._extract_tables(image)
            
            # Validate fields
            validated_fields = self._validate_fields(fields)
            
            # Calculate confidence
            confidence = self._calculate_confidence(validated_fields)
            
            return {
                "success": True,
                "form_type": detected_form_type,
                "confidence": confidence,
                "fields": validated_fields,
                "tables": tables,
                "metadata": {
                    "language": language,
                    "extraction_timestamp": datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Form extraction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "fields": {},
                "tables": []
            }
    
    def _decode_image(self, image_data: bytes) -> np.ndarray:
        """Decode image from bytes."""
        try:
            if isinstance(image_data, str):
                image_data = base64.b64decode(image_data)
            
            image = Image.open(io.BytesIO(image_data))
            
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return np.array(image)
            
        except Exception as e:
            logger.error(f"Failed to decode image: {e}")
            raise DataValidationException("Invalid image data")
    
    def _detect_form_type(self, image: np.ndarray) -> str:
        """Detect the type of form from image."""
        # Simplified detection based on aspect ratio
        h, w = image.shape[:2] if len(image.shape) >= 2 else (100, 100)
        aspect_ratio = w / h if h > 0 else 1.0
        
        if 0.7 < aspect_ratio < 0.8:
            return "application_form"
        elif 0.6 < aspect_ratio < 0.7:
            return "tax_form"
        else:
            return "general_form"
    
    async def _extract_fields(self, image: np.ndarray, form_type: str) -> Dict[str, Any]:
        """Extract fields from form."""
        # Simulated field extraction
        # In production, would use OCR and field detection
        
        fields = {}
        
        if form_type == "application_form":
            fields = {
                "name": {
                    "value": "John Doe",
                    "type": FieldType.TEXT.value,
                    "confidence": 0.92,
                    "location": {"x": 100, "y": 100, "width": 200, "height": 30}
                },
                "email": {
                    "value": "<EMAIL>",
                    "type": FieldType.EMAIL.value,
                    "confidence": 0.95,
                    "location": {"x": 100, "y": 150, "width": 200, "height": 30}
                },
                "phone": {
                    "value": "******-123-4567",
                    "type": FieldType.PHONE.value,
                    "confidence": 0.88,
                    "location": {"x": 100, "y": 200, "width": 200, "height": 30}
                },
                "date": {
                    "value": "01/15/2024",
                    "type": FieldType.DATE.value,
                    "confidence": 0.90,
                    "location": {"x": 100, "y": 250, "width": 100, "height": 30}
                },
                "signature": {
                    "value": "[Signature Present]",
                    "type": FieldType.SIGNATURE.value,
                    "confidence": 0.85,
                    "location": {"x": 100, "y": 300, "width": 150, "height": 50}
                }
            }
        elif form_type == "tax_form":
            fields = {
                "taxpayer_name": {
                    "value": "Jane Smith",
                    "type": FieldType.TEXT.value,
                    "confidence": 0.91,
                    "location": {"x": 100, "y": 100, "width": 200, "height": 30}
                },
                "ssn": {
                    "value": "***-**-6789",
                    "type": FieldType.TEXT.value,
                    "confidence": 0.87,
                    "location": {"x": 100, "y": 150, "width": 150, "height": 30}
                },
                "income": {
                    "value": "75000.00",
                    "type": FieldType.NUMBER.value,
                    "confidence": 0.93,
                    "location": {"x": 100, "y": 200, "width": 100, "height": 30}
                }
            }
        else:
            fields = {
                "field1": {
                    "value": "Sample Value",
                    "type": FieldType.TEXT.value,
                    "confidence": 0.80,
                    "location": {"x": 100, "y": 100, "width": 200, "height": 30}
                }
            }
        
        return fields
    
    async def _extract_tables(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Extract tables from form."""
        tables = []
        
        # Simulate table extraction
        if np.random.random() > 0.5:
            tables.append({
                "table_id": "table_1",
                "location": {"x": 50, "y": 400, "width": 500, "height": 200},
                "headers": ["Item", "Amount", "Category"],
                "rows": [
                    ["Medical Expenses", "$2,500", "Healthcare"],
                    ["Donations", "$1,000", "Charity"]
                ],
                "confidence": 0.86
            })
        
        return tables
    
    def _validate_fields(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """Validate extracted fields."""
        for field_name, field_data in fields.items():
            field_type_str = field_data.get("type", "text")
            value = field_data.get("value", "")
            
            # Apply validation based on field type
            valid = True
            
            if field_type_str == FieldType.EMAIL.value:
                pattern = self.field_patterns[FieldType.EMAIL]
                valid = bool(re.match(pattern, value))
            elif field_type_str == FieldType.PHONE.value:
                pattern = self.field_patterns[FieldType.PHONE]
                valid = bool(re.match(pattern, value))
            elif field_type_str == FieldType.DATE.value:
                pattern = self.field_patterns[FieldType.DATE]
                valid = bool(re.match(pattern, value))
            elif field_type_str == FieldType.NUMBER.value:
                try:
                    float(value.replace(',', '').replace('$', ''))
                    valid = True
                except ValueError:
                    valid = False
            
            field_data["valid"] = valid
        
        return fields
    
    def _calculate_confidence(self, fields: Dict[str, Any]) -> float:
        """Calculate overall extraction confidence."""
        if not fields:
            return 0.0
        
        confidences = [f.get("confidence", 0) for f in fields.values()]
        avg_confidence = sum(confidences) / len(confidences)
        
        # Adjust based on validation
        valid_count = sum(1 for f in fields.values() if f.get("valid", False))
        validation_ratio = valid_count / len(fields) if fields else 0
        
        return float(avg_confidence * 0.7 + validation_ratio * 0.3)