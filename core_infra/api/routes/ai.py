"""
Regulens AI - AI Insights Routes
Enterprise-grade AI-powered regulatory insights and analysis endpoints.
"""

import uuid
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, status, Query
from pydantic import BaseModel, Field
import structlog

from core_infra.api.auth import (
    get_current_user,
    verify_tenant_access,
    require_permission,
    UserInDB
)
from core_infra.config import get_settings
from core_infra.database.connection import get_database

# Initialize logging and settings
logger = structlog.get_logger(__name__)
settings = get_settings()

# Create router
router = APIRouter(tags=["AI Insights"])

# ============================================================================
# REQUEST/RESPONSE MODELS
# ============================================================================

class AnalysisRequest(BaseModel):
    """AI analysis request model."""
    analysis_type: str = Field(..., description="Type of analysis")
    entity_type: str = Field(..., description="Entity type (customer, transaction, document)")
    entity_id: str = Field(..., description="Entity ID to analyze")
    parameters: Dict[str, Any] = Field(default={}, description="Analysis parameters")

class RiskAnalysisResponse(BaseModel):
    """Risk analysis response model."""
    entity_id: str
    entity_type: str
    risk_score: float
    risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    confidence_level: float
    analysis_date: str

class ComplianceInsight(BaseModel):
    """Compliance insight response model."""
    insight_type: str
    title: str
    description: str
    severity: str
    affected_entities: List[str]
    recommendations: List[str]
    confidence: float
    created_at: str

class PredictiveAlert(BaseModel):
    """Predictive alert response model."""
    alert_id: str
    prediction_type: str
    entity_type: str
    entity_id: str
    predicted_risk: float
    probability: float
    time_horizon: str
    factors: List[str]
    recommended_actions: List[str]
    created_at: str

# ============================================================================
# AI ANALYSIS ENDPOINTS
# ============================================================================

@router.post("/analyze/risk", response_model=RiskAnalysisResponse)
async def analyze_risk(
    request: AnalysisRequest,
    current_user: UserInDB = Depends(require_permission("ai.analysis.trigger")),
    tenant_id: str = Depends(verify_tenant_access)
):
    """
    Perform AI-powered risk analysis on an entity.
    
    Requires permission: ai.analysis.trigger
    """
    try:
        async with get_database() as db:
            # Verify entity exists
            if request.entity_type == 'customer':
                entity = await db.fetchrow(
                    "SELECT * FROM customers WHERE id = $1 AND tenant_id = $2",
                    uuid.UUID(request.entity_id),
                    uuid.UUID(tenant_id)
                )
            elif request.entity_type == 'transaction':
                entity = await db.fetchrow(
                    "SELECT * FROM transactions WHERE id = $1 AND tenant_id = $2",
                    uuid.UUID(request.entity_id),
                    uuid.UUID(tenant_id)
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Unsupported entity type"
                )
            
            if not entity:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Entity not found"
                )
            
            # Perform AI risk analysis (simplified implementation)
            analysis_result = await _perform_ai_risk_analysis(db, entity, request.entity_type, request.parameters)
            
            # Save analysis result
            analysis_id = uuid.uuid4()
            await db.execute(
                """
                INSERT INTO ai_analysis_results (
                    id, tenant_id, entity_type, entity_id, analysis_type,
                    risk_score, risk_factors, recommendations, confidence_level,
                    parameters, created_by, created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
                """,
                analysis_id,
                uuid.UUID(tenant_id),
                request.entity_type,
                uuid.UUID(request.entity_id),
                request.analysis_type,
                analysis_result['risk_score'],
                analysis_result['risk_factors'],
                analysis_result['recommendations'],
                analysis_result['confidence_level'],
                request.parameters,
                uuid.UUID(current_user.id)
            )
            
            logger.info(f"AI risk analysis completed for {request.entity_type} {request.entity_id}")
            
            return RiskAnalysisResponse(
                entity_id=request.entity_id,
                entity_type=request.entity_type,
                risk_score=analysis_result['risk_score'],
                risk_factors=analysis_result['risk_factors'],
                recommendations=analysis_result['recommendations'],
                confidence_level=analysis_result['confidence_level'],
                analysis_date=datetime.utcnow().isoformat()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI risk analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="AI risk analysis failed"
        )

@router.get("/insights/compliance", response_model=List[ComplianceInsight])
async def get_compliance_insights(
    days: int = Query(7, description="Number of days to analyze"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    current_user: UserInDB = Depends(require_permission("ai.insights.read")),
    tenant_id: str = Depends(verify_tenant_access)
):
    """
    Get AI-generated compliance insights.
    
    Requires permission: ai.insights.read
    """
    try:
        async with get_database() as db:
            # Generate compliance insights (simplified implementation)
            insights = await _generate_compliance_insights(db, tenant_id, days, severity)
            
            return [
                ComplianceInsight(
                    insight_type=insight['type'],
                    title=insight['title'],
                    description=insight['description'],
                    severity=insight['severity'],
                    affected_entities=insight['affected_entities'],
                    recommendations=insight['recommendations'],
                    confidence=insight['confidence'],
                    created_at=insight['created_at']
                )
                for insight in insights
            ]
            
    except Exception as e:
        logger.error(f"Get compliance insights failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve compliance insights"
        )

@router.get("/predictions/alerts", response_model=List[PredictiveAlert])
async def get_predictive_alerts(
    time_horizon: str = Query("7d", description="Prediction time horizon"),
    min_probability: float = Query(0.7, description="Minimum probability threshold"),
    current_user: UserInDB = Depends(require_permission("ai.insights.read")),
    tenant_id: str = Depends(verify_tenant_access)
):
    """
    Get AI-generated predictive alerts.
    
    Requires permission: ai.insights.read
    """
    try:
        async with get_database() as db:
            # Generate predictive alerts (simplified implementation)
            alerts = await _generate_predictive_alerts(db, tenant_id, time_horizon, min_probability)
            
            return [
                PredictiveAlert(
                    alert_id=alert['id'],
                    prediction_type=alert['prediction_type'],
                    entity_type=alert['entity_type'],
                    entity_id=alert['entity_id'],
                    predicted_risk=alert['predicted_risk'],
                    probability=alert['probability'],
                    time_horizon=alert['time_horizon'],
                    factors=alert['factors'],
                    recommended_actions=alert['recommended_actions'],
                    created_at=alert['created_at']
                )
                for alert in alerts
            ]
            
    except Exception as e:
        logger.error(f"Get predictive alerts failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve predictive alerts"
        )

@router.get("/models/status")
async def get_model_status(
    current_user: UserInDB = Depends(require_permission("ai.models.manage")),
    tenant_id: str = Depends(verify_tenant_access)
):
    """
    Get AI model status and performance metrics.
    
    Requires permission: ai.models.manage
    """
    try:
        async with get_database() as db:
            try:
                # Query actual model status from database
                query = """
                    SELECT
                        model_name,
                        status,
                        version,
                        accuracy,
                        last_trained_at,
                        predictions_today
                    FROM ai_models
                    WHERE tenant_id = $1 AND is_active = true
                    ORDER BY model_name
                """

                models = await db.fetch(query, uuid.UUID(tenant_id))

                model_status = {}
                for model in models:
                    model_status[model['model_name']] = {
                        "status": model['status'],
                        "version": model['version'],
                        "accuracy": float(model['accuracy']) if model['accuracy'] else None,
                        "last_trained": model['last_trained_at'].isoformat() + "Z" if model['last_trained_at'] else None,
                        "predictions_today": model['predictions_today'] or 0
                    }

                return model_status

            except Exception as db_error:
                # If ai_models table doesn't exist, return empty status
                logger.warning(f"Could not query ai_models table: {db_error}")
                return {}
        
    except Exception as e:
        logger.error(f"Get model status failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model status"
        )

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

async def _perform_ai_risk_analysis(db, entity, entity_type, parameters):
    """Perform AI-powered risk analysis (simplified implementation)."""
    
    # This is a simplified implementation
    # In production, this would use actual ML models
    
    risk_factors = []
    risk_score = 0
    
    if entity_type == 'customer':
        # Analyze customer risk factors
        if entity['country'] in ['AF', 'IR', 'KP']:
            risk_factors.append({
                "factor": "high_risk_jurisdiction",
                "weight": 0.3,
                "description": "Customer from high-risk jurisdiction"
            })
            risk_score += 30
        
        if entity['pep_status']:
            risk_factors.append({
                "factor": "pep_status",
                "weight": 0.25,
                "description": "Customer is a Politically Exposed Person"
            })
            risk_score += 25
        
        if entity['risk_score'] > 70:
            risk_factors.append({
                "factor": "existing_high_risk",
                "weight": 0.2,
                "description": "Customer already flagged as high risk"
            })
            risk_score += 20
    
    elif entity_type == 'transaction':
        # Analyze transaction risk factors
        if entity['amount'] > 10000:
            risk_factors.append({
                "factor": "large_amount",
                "weight": 0.3,
                "description": "Transaction amount exceeds threshold"
            })
            risk_score += 30
        
        if entity['source_country'] != entity['destination_country']:
            risk_factors.append({
                "factor": "cross_border",
                "weight": 0.2,
                "description": "Cross-border transaction"
            })
            risk_score += 20
    
    # Generate recommendations
    recommendations = []
    if risk_score > 70:
        recommendations.extend([
            "Conduct enhanced due diligence",
            "Review transaction patterns",
            "Consider filing SAR if suspicious activity confirmed"
        ])
    elif risk_score > 40:
        recommendations.extend([
            "Monitor for additional suspicious activity",
            "Review customer documentation"
        ])
    else:
        recommendations.append("Continue standard monitoring")
    
    confidence_level = min(0.95, 0.6 + (len(risk_factors) * 0.1))
    
    return {
        'risk_score': min(risk_score, 100),
        'risk_factors': risk_factors,
        'recommendations': recommendations,
        'confidence_level': confidence_level
    }

async def _generate_compliance_insights(db, tenant_id, days, severity_filter):
    """Generate compliance insights (simplified implementation)."""
    
    insights = []
    
    # High-risk customer concentration insight
    high_risk_count = await db.fetchval(
        "SELECT COUNT(*) FROM customers WHERE tenant_id = $1 AND risk_score > 80",
        uuid.UUID(tenant_id)
    )
    
    if high_risk_count > 10:
        insights.append({
            'type': 'risk_concentration',
            'title': 'High Risk Customer Concentration',
            'description': f'You have {high_risk_count} high-risk customers requiring enhanced monitoring',
            'severity': 'medium',
            'affected_entities': [str(high_risk_count)],
            'recommendations': [
                'Review high-risk customer portfolio',
                'Implement enhanced monitoring procedures',
                'Consider risk mitigation strategies'
            ],
            'confidence': 0.85,
            'created_at': datetime.utcnow().isoformat()
        })
    
    # Overdue KYC reviews insight
    overdue_kyc = await db.fetchval(
        """
        SELECT COUNT(*) FROM customers 
        WHERE tenant_id = $1 AND last_kyc_review < NOW() - INTERVAL '1 year'
        """,
        uuid.UUID(tenant_id)
    )
    
    if overdue_kyc > 5:
        insights.append({
            'type': 'kyc_compliance',
            'title': 'Overdue KYC Reviews',
            'description': f'{overdue_kyc} customers have overdue KYC reviews',
            'severity': 'high',
            'affected_entities': [str(overdue_kyc)],
            'recommendations': [
                'Prioritize KYC review schedule',
                'Implement automated review reminders',
                'Consider customer risk-based review frequency'
            ],
            'confidence': 0.92,
            'created_at': datetime.utcnow().isoformat()
        })
    
    # Filter by severity if specified
    if severity_filter:
        insights = [i for i in insights if i['severity'] == severity_filter]
    
    return insights

async def _generate_predictive_alerts(db, tenant_id, time_horizon, min_probability):
    """Generate predictive alerts (simplified implementation)."""
    
    alerts = []
    
    # Predict potential AML violations based on transaction patterns
    high_velocity_customers = await db.fetch(
        """
        SELECT customer_id, COUNT(*) as tx_count, SUM(amount) as total_amount
        FROM transactions
        WHERE tenant_id = $1 AND created_at >= NOW() - INTERVAL '7 days'
        GROUP BY customer_id
        HAVING COUNT(*) > 10 AND SUM(amount) > 50000
        """,
        uuid.UUID(tenant_id)
    )
    
    for customer in high_velocity_customers:
        probability = min(0.95, 0.7 + (customer['tx_count'] / 100))
        if probability >= min_probability:
            alerts.append({
                'id': str(uuid.uuid4()),
                'prediction_type': 'aml_violation',
                'entity_type': 'customer',
                'entity_id': str(customer['customer_id']),
                'predicted_risk': 85.0,
                'probability': probability,
                'time_horizon': time_horizon,
                'factors': ['high_transaction_velocity', 'large_amounts'],
                'recommended_actions': [
                    'Enhanced transaction monitoring',
                    'Review customer activity patterns',
                    'Consider SAR filing if patterns continue'
                ],
                'created_at': datetime.utcnow().isoformat()
            })
    
    return alerts
