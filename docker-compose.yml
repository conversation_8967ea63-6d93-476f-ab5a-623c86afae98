version: '3.8'

services:
  # ============================================================================
  # DATABASE SERVICES (Using Supabase Cloud)
  # ============================================================================
  # PostgreSQL is provided by Supabase cloud service - no local database needed

  redis:
    image: redis:7-alpine
    container_name: regulens-redis
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - regulens-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ============================================================================
  # VECTOR DATABASE
  # ============================================================================
  
  qdrant:
    image: qdrant/qdrant:latest
    container_name: regulens-qdrant
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "6334:6334"
    networks:
      - regulens-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # CORE APPLICATION SERVICES
  # ============================================================================
  
  api:
    build:
      context: .
      dockerfile: core_infra/api/Dockerfile
    container_name: regulens-api
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
      - api_cache:/app/cache
    ports:
      - "${API_PORT:-8000}:8000"
    networks:
      - regulens-network
    depends_on:
      - redis
      - qdrant
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  regulatory-monitor:
    build:
      context: .
      dockerfile: core_infra/services/regulatory_monitor/Dockerfile
    container_name: regulens-regulatory-monitor
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - REGULATORY_MONITOR_ENABLED=${REGULATORY_MONITOR_ENABLED:-true}
      - REGULATORY_MONITOR_INTERVAL_MINUTES=${REGULATORY_MONITOR_INTERVAL_MINUTES:-60}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
    networks:
      - regulens-network
    depends_on:
      - redis
      - qdrant
    restart: unless-stopped

  compliance-engine:
    build:
      context: .
      dockerfile: core_infra/services/compliance_engine/Dockerfile
    container_name: regulens-compliance-engine
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - AML_MONITORING_ENABLED=${AML_MONITORING_ENABLED:-true}
      - TRANSACTION_MONITORING_REAL_TIME=${TRANSACTION_MONITORING_REAL_TIME:-true}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
    networks:
      - regulens-network
    depends_on:
      - redis
    restart: unless-stopped

  ai-insights:
    build:
      context: .
      dockerfile: core_infra/ai/Dockerfile
    container_name: regulens-ai-insights
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CLAUDE_API_KEY=${CLAUDE_API_KEY}
      - AI_REGULATORY_INSIGHTS_ENABLED=${AI_REGULATORY_INSIGHTS_ENABLED:-true}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
      - ./models_cache:/app/models_cache
    networks:
      - regulens-network
    depends_on:
      - redis
      - qdrant
    restart: unless-stopped

  # ============================================================================
  # TASK QUEUE WORKERS
  # ============================================================================
  
  celery-worker:
    build:
      context: .
      dockerfile: core_infra/workers/Dockerfile
    container_name: regulens-celery-worker
    command: celery -A core_infra.workers.celery_app worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/2
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
    networks:
      - regulens-network
    depends_on:
      - redis
    restart: unless-stopped

  celery-beat:
    build:
      context: .
      dockerfile: core_infra/workers/Dockerfile
    container_name: regulens-celery-beat
    command: celery -A core_infra.workers.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@redis:6379/1
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@redis:6379/2
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - ./core_infra:/app/core_infra:ro
      - ./logs:/app/logs
    networks:
      - regulens-network
    depends_on:
      - redis
    restart: unless-stopped

  # ============================================================================
  # FRONTEND
  # ============================================================================
  
  frontend:
    build:
      context: .
      dockerfile: ui/Dockerfile
    container_name: regulens-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:${API_PORT:-8000}
      - REACT_APP_ENVIRONMENT=${APP_ENVIRONMENT:-production}
      - REACT_APP_SUPABASE_URL=${SUPABASE_URL}
      - REACT_APP_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    volumes:
      - ./ui/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "3000:80"
    networks:
      - regulens-network
    depends_on:
      - api
    restart: unless-stopped

  # ============================================================================
  # MONITORING AND OBSERVABILITY
  # ============================================================================
  
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: regulens-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"
      - "14268:14268"
      - "6831:6831/udp"
    networks:
      - regulens-network
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: regulens-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    networks:
      - regulens-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: regulens-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3001:3000"
    networks:
      - regulens-network
    depends_on:
      - prometheus
    restart: unless-stopped

  # ============================================================================
  # BUSINESS INTELLIGENCE (Disabled - using Supabase Analytics)
  # ============================================================================
  # Metabase removed since we're using Supabase cloud for database analytics

  # ============================================================================
  # NGINX REVERSE PROXY (PRODUCTION)
  # ============================================================================
  
  nginx:
    image: nginx:alpine
    container_name: regulens-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - regulens-network
    depends_on:
      - api
      - frontend
    restart: unless-stopped
    profiles:
      - production

# ============================================================================
# NETWORKS
# ============================================================================

networks:
  regulens-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# VOLUMES
# ============================================================================

volumes:
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  api_cache:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
 