# Regulens AI - Blue-Green Deployment Pipeline
# Zero-downtime deployment strategy with automated health checks and rollback

name: Blue-Green Deployment

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: regulens-ai
  HEALTH_CHECK_TIMEOUT: 300
  ROLLBACK_TIMEOUT: 180

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Run security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ steps.meta.outputs.tags }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-blue:
    needs: build-and-test
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
    - name: Checkout deployment configs
      uses: actions/checkout@v4
      with:
        path: configs
        
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        kubectl config current-context
        
    - name: Determine deployment slot
      id: slot
      run: |
        # Check which slot is currently active
        CURRENT_SLOT=$(kubectl get service regulens-ai-active -o jsonpath='{.spec.selector.slot}' 2>/dev/null || echo "green")
        if [ "$CURRENT_SLOT" = "blue" ]; then
          NEW_SLOT="green"
        else
          NEW_SLOT="blue"
        fi
        echo "current-slot=$CURRENT_SLOT" >> $GITHUB_OUTPUT
        echo "new-slot=$NEW_SLOT" >> $GITHUB_OUTPUT
        echo "Deploying to $NEW_SLOT slot (current: $CURRENT_SLOT)"
        
    - name: Deploy to new slot
      env:
        IMAGE_TAG: ${{ needs.build-and-test.outputs.image-tag }}
        SLOT: ${{ steps.slot.outputs.new-slot }}
      run: |
        # Update deployment manifest
        envsubst < configs/k8s/deployment-template.yaml > deployment-$SLOT.yaml
        
        # Apply deployment
        kubectl apply -f deployment-$SLOT.yaml
        
        # Wait for deployment to be ready
        kubectl rollout status deployment/regulens-ai-$SLOT --timeout=600s
        
    - name: Run health checks
      id: health
      env:
        SLOT: ${{ steps.slot.outputs.new-slot }}
      run: |
        # Get service endpoint for new slot
        SERVICE_IP=$(kubectl get service regulens-ai-$SLOT -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -z "$SERVICE_IP" ]; then
          SERVICE_IP=$(kubectl get service regulens-ai-$SLOT -o jsonpath='{.spec.clusterIP}')
        fi
        
        echo "Testing health endpoint: http://$SERVICE_IP:8000/health"
        
        # Health check with retry
        for i in {1..30}; do
          if curl -f -s "http://$SERVICE_IP:8000/health" > /dev/null; then
            echo "Health check passed on attempt $i"
            echo "health-status=healthy" >> $GITHUB_OUTPUT
            break
          fi
          echo "Health check failed on attempt $i, retrying in 10 seconds..."
          sleep 10
          if [ $i -eq 30 ]; then
            echo "Health check failed after 30 attempts"
            echo "health-status=unhealthy" >> $GITHUB_OUTPUT
            exit 1
          fi
        done
        
    - name: Run smoke tests
      env:
        SLOT: ${{ steps.slot.outputs.new-slot }}
      run: |
        # Run comprehensive smoke tests
        SERVICE_IP=$(kubectl get service regulens-ai-$SLOT -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -z "$SERVICE_IP" ]; then
          SERVICE_IP=$(kubectl get service regulens-ai-$SLOT -o jsonpath='{.spec.clusterIP}')
        fi
        
        BASE_URL="http://$SERVICE_IP:8000"
        
        # Test critical endpoints
        echo "Testing API endpoints..."
        
        # Health check
        curl -f "$BASE_URL/health" || exit 1
        
        # Metrics endpoint
        curl -f "$BASE_URL/metrics" || exit 1
        
        # Database health
        curl -f "$BASE_URL/health/database" || exit 1
        
        # Cache health
        curl -f "$BASE_URL/health/cache" || exit 1
        
        echo "All smoke tests passed"
        
    - name: Switch traffic (Blue-Green cutover)
      if: steps.health.outputs.health-status == 'healthy'
      env:
        NEW_SLOT: ${{ steps.slot.outputs.new-slot }}
        OLD_SLOT: ${{ steps.slot.outputs.current-slot }}
      run: |
        echo "Switching traffic from $OLD_SLOT to $NEW_SLOT"
        
        # Update active service to point to new slot
        kubectl patch service regulens-ai-active -p '{"spec":{"selector":{"slot":"'$NEW_SLOT'"}}}'
        
        # Wait for service to update
        sleep 30
        
        # Verify traffic switch
        kubectl get service regulens-ai-active -o yaml
        
        echo "Traffic successfully switched to $NEW_SLOT"
        
    - name: Post-deployment verification
      env:
        NEW_SLOT: ${{ steps.slot.outputs.new-slot }}
      run: |
        # Verify the active service is working
        ACTIVE_IP=$(kubectl get service regulens-ai-active -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -z "$ACTIVE_IP" ]; then
          ACTIVE_IP=$(kubectl get service regulens-ai-active -o jsonpath='{.spec.clusterIP}')
        fi
        
        echo "Verifying active service at $ACTIVE_IP"
        
        # Extended health check
        for i in {1..10}; do
          if curl -f -s "http://$ACTIVE_IP:8000/health" | grep -q "healthy"; then
            echo "Post-deployment verification passed"
            break
          fi
          sleep 5
          if [ $i -eq 10 ]; then
            echo "Post-deployment verification failed"
            exit 1
          fi
        done
        
    - name: Clean up old deployment
      if: success()
      env:
        OLD_SLOT: ${{ steps.slot.outputs.current-slot }}
      run: |
        echo "Cleaning up old deployment in $OLD_SLOT slot"
        
        # Scale down old deployment
        kubectl scale deployment regulens-ai-$OLD_SLOT --replicas=0
        
        # Keep the deployment for potential rollback, but remove resources
        echo "Old deployment scaled down, keeping for potential rollback"

  rollback:
    needs: [build-and-test, deploy-blue]
    runs-on: ubuntu-latest
    if: failure() && needs.deploy-blue.result == 'failure'
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: Configure kubectl
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        
    - name: Determine rollback slot
      id: rollback-slot
      run: |
        # Get the slot that failed deployment
        FAILED_SLOT=$(kubectl get service regulens-ai-active -o jsonpath='{.spec.selector.slot}' 2>/dev/null || echo "blue")
        if [ "$FAILED_SLOT" = "blue" ]; then
          ROLLBACK_SLOT="green"
        else
          ROLLBACK_SLOT="blue"
        fi
        echo "rollback-slot=$ROLLBACK_SLOT" >> $GITHUB_OUTPUT
        echo "Rolling back to $ROLLBACK_SLOT slot"
        
    - name: Execute rollback
      env:
        ROLLBACK_SLOT: ${{ steps.rollback-slot.outputs.rollback-slot }}
      run: |
        echo "Executing rollback to $ROLLBACK_SLOT"
        
        # Check if rollback slot has a healthy deployment
        ROLLBACK_REPLICAS=$(kubectl get deployment regulens-ai-$ROLLBACK_SLOT -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
        
        if [ "$ROLLBACK_REPLICAS" -gt "0" ]; then
          # Switch traffic back to previous slot
          kubectl patch service regulens-ai-active -p '{"spec":{"selector":{"slot":"'$ROLLBACK_SLOT'"}}}'
          echo "Traffic switched back to $ROLLBACK_SLOT"
        else
          # Scale up previous deployment
          kubectl scale deployment regulens-ai-$ROLLBACK_SLOT --replicas=3
          kubectl rollout status deployment/regulens-ai-$ROLLBACK_SLOT --timeout=300s
          
          # Switch traffic
          kubectl patch service regulens-ai-active -p '{"spec":{"selector":{"slot":"'$ROLLBACK_SLOT'"}}}'
          echo "Previous deployment restored and traffic switched"
        fi
        
    - name: Verify rollback
      env:
        ROLLBACK_SLOT: ${{ steps.rollback-slot.outputs.rollback-slot }}
      run: |
        # Verify rollback was successful
        ACTIVE_IP=$(kubectl get service regulens-ai-active -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [ -z "$ACTIVE_IP" ]; then
          ACTIVE_IP=$(kubectl get service regulens-ai-active -o jsonpath='{.spec.clusterIP}')
        fi
        
        for i in {1..10}; do
          if curl -f -s "http://$ACTIVE_IP:8000/health" | grep -q "healthy"; then
            echo "Rollback verification passed"
            break
          fi
          sleep 5
          if [ $i -eq 10 ]; then
            echo "Rollback verification failed"
            exit 1
          fi
        done
        
    - name: Notify rollback
      if: always()
      run: |
        echo "Deployment failed and rollback was executed"
        # Here you would typically send notifications to Slack, email, etc.

  notify:
    needs: [build-and-test, deploy-blue]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      env:
        DEPLOYMENT_STATUS: ${{ needs.deploy-blue.result }}
        IMAGE_TAG: ${{ needs.build-and-test.outputs.image-tag }}
      run: |
        if [ "$DEPLOYMENT_STATUS" = "success" ]; then
          echo "✅ Deployment successful: $IMAGE_TAG"
        else
          echo "❌ Deployment failed: $IMAGE_TAG"
        fi
        
        # Here you would send notifications to your team
        # Example: Slack webhook, email, etc.
