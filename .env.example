# ============================================================================
# REGULENS AI - FINANCIAL COMPLIANCE PLATFORM CONFIGURATION
# Example environment configuration - Copy to .env and update values
# ============================================================================

# Application Configuration
APP_NAME=Regulens AI Financial Compliance Platform
APP_VERSION=1.0.0
APP_ENVIRONMENT=production
DEBUG=false
API_VERSION=v1
API_PORT=8000
API_HOST=0.0.0.0

# Security Configuration - GENERATE SECURE VALUES
JWT_SECRET_KEY=your-jwt-secret-key-change-this-to-secure-random-string
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30
ENCRYPTION_KEY=your-encryption-key-change-this-to-secure-random-string
BCRYPT_ROUNDS=12

# Database Configuration - UPDATE WITH YOUR SUPABASE CREDENTIALS
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Redis Configuration - CHANGE PASSWORD
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-secure-redis-password-change-this

# Storage Configuration
STORAGE_PROVIDER=supabase
SUPABASE_STORAGE_BUCKET=compliance-documents

# AI Configuration - ADD YOUR API KEYS
OPENAI_API_KEY=your-openai-api-key
CLAUDE_API_KEY=your-claude-api-key

# LangSmith Configuration
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your-langsmith-api-key
LANGCHAIN_PROJECT=regulens-ai-compliance

# Regulatory Data Sources API Keys
SEC_API_KEY=your-sec-api-key
FCA_API_KEY=your-fca-api-key
ECB_API_KEY=your-ecb-api-key
FINCEN_API_KEY=your-fincen-api-key

# External Data Provider Credentials
# OFAC Sanctions Screening (No API key required - uses public data)
OFAC_UPDATE_INTERVAL_HOURS=24

# Experian Credit Bureau
EXPERIAN_CLIENT_ID=your-experian-client-id
EXPERIAN_CLIENT_SECRET=your-experian-client-secret
EXPERIAN_SUBSCRIBER_CODE=your-experian-subscriber-code
EXPERIAN_SUB_CODE=your-experian-sub-code
EXPERIAN_USE_SANDBOX=true

# Refinitiv Market Data
REFINITIV_API_KEY=your-refinitiv-api-key
REFINITIV_APP_ID=your-refinitiv-app-id
REFINITIV_USERNAME=your-refinitiv-username
REFINITIV_PASSWORD=your-refinitiv-password

# Dow Jones Risk & Compliance (for PEP screening)
DOWJONES_API_KEY=your-dowjones-api-key
DOWJONES_USER_ID=your-dowjones-user-id
DOWJONES_PASSWORD=your-dowjones-password

# EU Sanctions API (if using commercial provider)
EU_SANCTIONS_API_KEY=your-eu-sanctions-api-key
EU_SANCTIONS_BASE_URL=https://api.eu-sanctions-provider.com

# UN Sanctions API (if using commercial provider)
UN_SANCTIONS_API_KEY=your-un-sanctions-api-key
UN_SANCTIONS_BASE_URL=https://api.un-sanctions-provider.com

# GRC System Integrations
# RSA Archer
ARCHER_BASE_URL=https://your-archer-instance.com
ARCHER_USERNAME=your-archer-username
ARCHER_PASSWORD=your-archer-password
ARCHER_INSTANCE_NAME=Default
ARCHER_DOMAIN=YOUR_DOMAIN
ARCHER_RISK_APPLICATION_ID=123
ARCHER_RISK_TITLE_FIELD_ID=456
ARCHER_RISK_DESCRIPTION_FIELD_ID=457
ARCHER_RISK_CATEGORY_FIELD_ID=458
ARCHER_RISK_SEVERITY_FIELD_ID=459
ARCHER_RISK_STATUS_FIELD_ID=460
ARCHER_RISK_OWNER_FIELD_ID=461
ARCHER_RISK_DATE_FIELD_ID=462
ARCHER_RISK_MITIGATION_FIELD_ID=463

# ServiceNow GRC
SERVICENOW_INSTANCE_URL=https://your-instance.service-now.com
SERVICENOW_USERNAME=your-servicenow-username
SERVICENOW_PASSWORD=your-servicenow-password
SERVICENOW_RISK_TABLE=sn_risk_framework_risk
SERVICENOW_CONTROL_TABLE=sn_risk_framework_control
SERVICENOW_INCIDENT_TABLE=incident

# MetricStream
METRICSTREAM_BASE_URL=https://your-metricstream-instance.com
METRICSTREAM_USERNAME=your-metricstream-username
METRICSTREAM_PASSWORD=your-metricstream-password
METRICSTREAM_API_KEY=your-metricstream-api-key

# Core Banking System Integrations
# Temenos T24/Transact
T24_BASE_URL=https://your-t24-instance.com
T24_AUTH_TYPE=oauth
T24_CLIENT_ID=your-t24-client-id
T24_CLIENT_SECRET=your-t24-client-secret
T24_USERNAME=your-t24-username
T24_PASSWORD=your-t24-password
T24_BATCH_SIZE=1000
T24_AMOUNT_THRESHOLD=10000
T24_MONITORED_CURRENCIES=USD,EUR,GBP
T24_WEBHOOK_AUTH_TOKEN=your-webhook-auth-token

# Oracle Flexcube
FLEXCUBE_BASE_URL=https://your-flexcube-instance.com
FLEXCUBE_USERNAME=your-flexcube-username
FLEXCUBE_PASSWORD=your-flexcube-password
FLEXCUBE_BRANCH_CODE=001
FLEXCUBE_BATCH_SIZE=1000
FLEXCUBE_AMOUNT_THRESHOLD=10000
FLEXCUBE_MONITORED_CURRENCIES=USD,EUR,GBP

# Infosys Finacle
FINACLE_BASE_URL=https://your-finacle-instance.com
FINACLE_USERNAME=your-finacle-username
FINACLE_PASSWORD=your-finacle-password
FINACLE_BRANCH_CODE=001
FINACLE_BATCH_SIZE=1000
FINACLE_AMOUNT_THRESHOLD=10000
FINACLE_MONITORED_CURRENCIES=USD,EUR,GBP

# External Data Provider Integrations
# Refinitiv Market Data
REFINITIV_API_KEY=your-refinitiv-api-key
REFINITIV_USERNAME=your-refinitiv-username
REFINITIV_PASSWORD=your-refinitiv-password
REFINITIV_APP_ID=RegulensAI
REFINITIV_BASE_URL=https://api.refinitiv.com
REFINITIV_RATE_LIMIT_PER_MINUTE=1000
REFINITIV_TIMEOUT_SECONDS=30

# Experian Credit Services
EXPERIAN_CLIENT_ID=your-experian-client-id
EXPERIAN_CLIENT_SECRET=your-experian-client-secret
EXPERIAN_SUBSCRIBER_CODE=your-subscriber-code
EXPERIAN_SUB_CODE=your-sub-code
EXPERIAN_USE_SANDBOX=true
EXPERIAN_BASE_URL_SANDBOX=https://sandbox-api.experian.com
EXPERIAN_BASE_URL_PRODUCTION=https://api.experian.com

# EU Sanctions Data
EU_SANCTIONS_CACHE_HOURS=24
EU_SANCTIONS_AUTO_UPDATE=true
EU_SANCTIONS_UPDATE_INTERVAL_HOURS=6

# UN Sanctions Data
UN_SANCTIONS_CACHE_HOURS=24
UN_SANCTIONS_AUTO_UPDATE=true
UN_SANCTIONS_UPDATE_INTERVAL_HOURS=6

# OFAC Sanctions Data
OFAC_CACHE_HOURS=12
OFAC_AUTO_UPDATE=true
OFAC_UPDATE_INTERVAL_HOURS=4
OFAC_BASE_URL=https://www.treasury.gov/ofac/downloads

# GRC System Integrations
# RSA Archer
ARCHER_BASE_URL=https://your-archer-instance.com
ARCHER_USERNAME=your-archer-username
ARCHER_PASSWORD=your-archer-password
ARCHER_INSTANCE_NAME=production
ARCHER_DOMAIN=your-domain
ARCHER_TIMEOUT_SECONDS=60
ARCHER_SYNC_INTERVAL_HOURS=6

# ServiceNow GRC
SERVICENOW_INSTANCE_URL=https://your-instance.service-now.com
SERVICENOW_USERNAME=your-servicenow-username
SERVICENOW_PASSWORD=your-servicenow-password
SERVICENOW_TIMEOUT_SECONDS=30
SERVICENOW_SYNC_INTERVAL_HOURS=4
SERVICENOW_MAX_RECORDS_PER_REQUEST=1000

# MetricStream
METRICSTREAM_BASE_URL=https://your-instance.metricstream.com
METRICSTREAM_AUTH_TYPE=api_key
METRICSTREAM_API_KEY=your-metricstream-api-key
METRICSTREAM_CLIENT_ID=your-oauth-client-id
METRICSTREAM_CLIENT_SECRET=your-oauth-client-secret
METRICSTREAM_TIMEOUT_SECONDS=45
METRICSTREAM_SYNC_INTERVAL_HOURS=8
METRICSTREAM_MAX_RECORDS_PER_REQUEST=500

# AML/KYC Configuration
AML_MONITORING_ENABLED=true
TRANSACTION_MONITORING_REAL_TIME=true
KYC_AUTO_VERIFICATION_ENABLED=false
SANCTIONS_SCREENING_ENABLED=true

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=your-smtp-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Regulens AI Compliance Platform

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_NUMBER=+**********

# Collaboration Platform Webhooks
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK

# Observability and Monitoring
JAEGER_ENABLED=true
LOG_LEVEL=INFO
METRICS_ENABLED=true

# Qdrant Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=your-qdrant-api-key

# Security Features
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20
TENANT_ISOLATION_ENABLED=true
VIRUS_SCAN_ENABLED=true

# Monitoring Passwords - CHANGE THESE
GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password

# Feature Flags
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_PREDICTIVE_COMPLIANCE=true
FEATURE_AUTOMATED_REPORTING=true
FEATURE_REAL_TIME_MONITORING=true

# Regulatory Monitoring
REGULATORY_MONITOR_ENABLED=true
REGULATORY_MONITOR_INTERVAL_MINUTES=60

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ============================================================================
# SECURITY NOTES:
# 1. Generate secure random strings for all secret keys
# 2. Use strong passwords (minimum 16 characters)
# 3. Never commit this file with real credentials to version control
# 4. Rotate credentials regularly
# 5. Use different passwords for each service
# ============================================================================
