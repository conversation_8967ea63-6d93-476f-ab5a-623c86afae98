# Default values for RegulensAI
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Image configuration
image:
  registry: regulensai
  pullPolicy: IfNotPresent
  tag: "1.0.0"

# Environment configuration
environment: production

# Replica counts
replicaCount:
  api: 3
  notifications: 2
  integrations: 2
  worker: 2

# Service configuration
service:
  type: ClusterIP
  api:
    port: 8000
    targetPort: 8000
  notifications:
    port: 8001
    targetPort: 8001
  integrations:
    port: 8002
    targetPort: 8002

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api.regulensai.com
      paths:
        - path: /
          pathType: Prefix
          service: regulensai-api
  tls:
    - secretName: regulensai-tls
      hosts:
        - api.regulensai.com

# Resource configuration
resources:
  api:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  notifications:
    requests:
      memory: "256Mi"
      cpu: "125m"
    limits:
      memory: "512Mi"
      cpu: "250m"
  integrations:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  worker:
    requests:
      memory: "256Mi"
      cpu: "125m"
    limits:
      memory: "512Mi"
      cpu: "250m"

# Autoscaling configuration
autoscaling:
  enabled: true
  api:
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  notifications:
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  integrations:
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Security configuration
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

podSecurityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
    - ALL

# Service account configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Network policies
networkPolicy:
  enabled: true
  ingress:
    enabled: true
  egress:
    enabled: true

# Configuration
config:
  # Application settings
  logLevel: "INFO"
  debug: false
  
  # API settings
  apiHost: "0.0.0.0"
  apiPort: 8000
  apiWorkers: 4
  apiTimeout: 30
  
  # Database settings
  dbPoolSize: 20
  dbMaxOverflow: 30
  dbPoolTimeout: 30
  dbPoolRecycle: 3600
  
  # Redis settings
  redisDb: 0
  redisPoolSize: 10
  redisTimeout: 5
  
  # External data provider settings
  ofac:
    cacheHours: 12
    autoUpdate: true
    updateIntervalHours: 4
    baseUrl: "https://www.treasury.gov/ofac/downloads"
  
  euSanctions:
    cacheHours: 24
    autoUpdate: true
    updateIntervalHours: 6
  
  unSanctions:
    cacheHours: 24
    autoUpdate: true
    updateIntervalHours: 6
  
  # Notification settings
  notifications:
    batchSize: 100
    maxConcurrentBatches: 10
    retryAttempts: 3
    retryDelay: 5
  
  # GRC integration settings
  grc:
    syncIntervalHours: 6
    batchSize: 500
    timeoutSeconds: 60
  
  # Monitoring settings
  monitoring:
    metricsEnabled: true
    metricsPort: 9090
    tracingEnabled: true
    jaegerEndpoint: "http://jaeger-collector:14268/api/traces"
  
  # Feature flags
  features:
    enhancedNotifications: true
    externalDataProviders: true
    grcConnectors: true
    advancedAnalytics: false
  
  # Rate limiting
  rateLimit:
    requestsPerMinute: 1000
    burstSize: 100
  
  # Security settings
  security:
    corsOrigins: "https://app.regulensai.com,https://admin.regulensai.com"
    allowedHosts: "api.regulensai.com,*.regulensai.com"

# Secrets configuration (references to external secrets)
secrets:
  # Database
  databaseUrl: ""
  
  # Encryption
  encryptionKey: ""
  jwtSecret: ""
  
  # Redis
  redisPassword: ""
  
  # External APIs
  refinitiv:
    apiKey: ""
    username: ""
    password: ""
  
  experian:
    clientId: ""
    clientSecret: ""
    subscriberCode: ""
    subCode: ""
  
  # Notification services
  smtp:
    host: ""
    port: ""
    username: ""
    password: ""
  
  twilio:
    accountSid: ""
    authToken: ""
    phoneNumber: ""
  
  # Webhooks
  webhookSigningSecret: ""

# External dependencies
redis:
  enabled: true
  auth:
    enabled: true
    password: "redis-password"
  master:
    persistence:
      enabled: true
      size: 8Gi
  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 8Gi

postgresql:
  enabled: false  # Using external Supabase
  auth:
    postgresPassword: "postgres-password"
    database: "regulensai"

nginx:
  enabled: true
  replicaCount: 2
  service:
    type: LoadBalancer

# Monitoring stack
monitoring:
  enabled: true
  
  prometheus:
    enabled: true
    server:
      persistentVolume:
        enabled: true
        size: 50Gi
      retention: "30d"
    alertmanager:
      enabled: true
      persistentVolume:
        enabled: true
        size: 10Gi
  
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
    adminPassword: "admin-password"
    dashboards:
      default:
        regulensai-overview:
          gnetId: 12345
          revision: 1
          datasource: Prometheus
  
  jaeger:
    enabled: true
    storage:
      type: elasticsearch
    elasticsearch:
      enabled: true

# Backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: "30d"
  storage:
    type: s3
    bucket: "regulensai-backups"
    region: "us-east-1"

# Migration configuration
migration:
  enabled: true
  image:
    repository: regulensai/migrations
    tag: "1.0.0"
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "256Mi"
      cpu: "200m"
