apiVersion: v2
name: regulensai
description: RegulensAI Compliance and Risk Management Platform
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - compliance
  - risk-management
  - fintech
  - regulations
  - grc

home: https://regulensai.com
sources:
  - https://github.com/regulensai/platform

maintainers:
  - name: RegulensAI Platform Team
    email: <EMAIL>
    url: https://regulensai.com

annotations:
  category: Business Applications
  licenses: Proprietary

dependencies:
  - name: redis
    version: "17.3.7"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: postgresql
    version: "12.1.2"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: nginx
    version: "13.2.10"
    repository: "https://charts.bitnami.com/bitnami"
    condition: nginx.enabled
  - name: prometheus
    version: "15.18.0"
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.50.7"
    repository: "https://grafana.github.io/helm-charts"
    condition: monitoring.grafana.enabled
  - name: jaeger
    version: "0.69.1"
    repository: "https://jaegertracing.github.io/helm-charts"
    condition: monitoring.jaeger.enabled
