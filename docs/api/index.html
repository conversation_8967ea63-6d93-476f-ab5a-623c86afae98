<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RegulensAI API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.9.0/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.9.0/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *, *:before, *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }

        .swagger-ui .topbar {
            background-color: #1976d2;
            border-bottom: 1px solid #1565c0;
        }

        .swagger-ui .topbar .download-url-wrapper {
            display: none;
        }

        .swagger-ui .info .title {
            color: #1976d2;
        }

        .swagger-ui .scheme-container {
            background: #fff;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.15);
        }

        .custom-header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .quick-links {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .quick-links h3 {
            color: #1976d2;
            margin-top: 0;
        }

        .quick-links ul {
            list-style: none;
            padding: 0;
        }

        .quick-links li {
            margin: 10px 0;
        }

        .quick-links a {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
        }

        .quick-links a:hover {
            text-decoration: underline;
        }

        .demo-credentials {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 15px;
            margin: 20px;
        }

        .demo-credentials h4 {
            color: #1976d2;
            margin-top: 0;
        }

        .demo-credentials code {
            background: #fff;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
    </style>
</head>

<body>
    <div class="custom-header">
        <h1>RegulensAI API</h1>
        <p>Enterprise Financial Compliance Platform</p>
    </div>

    <div class="demo-credentials">
        <h4>🚀 Demo Credentials</h4>
        <p>Use these credentials to test the API endpoints:</p>
        <ul>
            <li><strong>Email:</strong> <code><EMAIL></code></li>
            <li><strong>Password:</strong> <code>admin123</code></li>
        </ul>
        <p><em>First, authenticate using the <strong>/auth/login</strong> endpoint, then use the returned token for other requests.</em></p>
    </div>

    <div class="quick-links">
        <h3>📋 Quick Links</h3>
        <ul>
            <li><a href="#/Authentication">🔐 Authentication</a> - Login and token management</li>
            <li><a href="#/Users">👥 User Management</a> - Create and manage users</li>
            <li><a href="#/Compliance">📊 Compliance</a> - Tasks and reports</li>
            <li><a href="#/Training%20Portal">🎓 Training Portal</a> - Learning modules</li>
            <li><a href="#/Dashboard">📈 Dashboard</a> - Metrics and analytics</li>
            <li><a href="#/System">⚡ System Health</a> - Status monitoring</li>
        </ul>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // Begin Swagger UI call region
            const ui = SwaggerUIBundle({
                url: './openapi_spec.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                tryItOutEnabled: true,
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                onComplete: function() {
                    console.log("RegulensAI API Documentation loaded successfully");
                },
                requestInterceptor: function(request) {
                    // Add custom headers or modify requests here
                    console.log("API Request:", request);
                    return request;
                },
                responseInterceptor: function(response) {
                    // Handle responses here
                    console.log("API Response:", response);
                    return response;
                },
                docExpansion: "list",
                defaultModelsExpandDepth: 2,
                defaultModelExpandDepth: 2,
                displayOperationId: false,
                displayRequestDuration: true,
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tagsSorter: "alpha",
                operationsSorter: "alpha"
            });
            // End Swagger UI call region

            window.ui = ui;
        };
    </script>
</body>
</html>
