openapi: 3.0.3
info:
  title: RegulensAI API
  description: |
    # RegulensAI Enterprise Financial Compliance Platform API

    **Enterprise-grade financial compliance platform** for banks, financial institutions, and fintech companies.

    ## 🚀 Quick Start

    ### Authentication
    All endpoints require JWT authentication. First, obtain a token:

    ```bash
    curl -X POST "http://localhost:8000/api/v1/auth/login" \
      -H "Content-Type: application/json" \
      -d '{"email": "<EMAIL>", "password": "admin123"}'
    ```

    Then include the token in subsequent requests:
    ```bash
    curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
      "http://localhost:8000/api/v1/regulatory/sources"
    ```

    ### Multi-tenancy
    Include your tenant ID in all requests:
    ```bash
    curl -H "X-Tenant-ID: your-tenant-id" \
         -H "Authorization: Bearer YOUR_JWT_TOKEN" \
         "http://localhost:8000/api/v1/compliance/tasks"
    ```

    ## 📋 Core Features

    - **Authentication & Authorization**: JWT-based auth with RBAC
    - **Compliance Management**: Comprehensive compliance tracking and reporting
    - **AML/KYC Processing**: Anti-Money Laundering and Know Your Customer workflows
    - **Regulatory Monitoring**: Real-time regulatory change tracking
    - **Training Portal**: Interactive compliance training modules
    - **AI-Powered Insights**: Machine learning-driven risk analysis
    - **External Integrations**: GRC systems, banking platforms, data providers
    - **Real-time Notifications**: Multi-channel alert system

    ## 🔒 Security

    - OWASP Top 10 security compliance
    - End-to-end encryption
    - Role-based access control (RBAC)
    - Audit logging and monitoring
    - Rate limiting and DDoS protection

    ## 📊 Rate Limiting

    API requests are rate limited to **1000 requests per minute per tenant**.

    ## ⚠️ Error Handling

    All errors follow **RFC 7807 Problem Details** format with structured error responses.

  version: 1.0.0
  contact:
    name: RegulensAI Support
    email: <EMAIL>
    url: https://docs.regulens.ai
  license:
    name: Proprietary
    url: https://regulens.ai/license

servers:
  - url: https://api.regulens.ai/v1
    description: Production server
  - url: https://staging-api.regulens.ai/v1
    description: Staging server
  - url: http://localhost:8000/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  # ============================================================================
  # SYSTEM & HEALTH ENDPOINTS
  # ============================================================================

  /health:
    get:
      tags:
        - System
      summary: Health check
      description: Check system health and status
      operationId: healthCheck
      security: []
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  service:
                    type: string
                    example: "regulens-ai"
                  timestamp:
                    type: string
                    format: date-time
                  version:
                    type: string
                    example: "1.0.0"

  # ============================================================================
  # AUTHENTICATION ENDPOINTS
  # ============================================================================

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: |
        Authenticate user and return JWT tokens.

        **Demo Credentials:**
        - Email: <EMAIL>
        - Password: admin123
      operationId: login
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            examples:
              admin_login:
                summary: Admin login example
                value:
                  email: "<EMAIL>"
                  password: "admin123"
                  remember_me: false
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Logout user and invalidate tokens
      operationId: logout
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogoutResponse'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: Refresh access token using refresh token
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
                  description: Valid refresh token
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'

  /auth/me:
    get:
      tags:
        - Authentication
      summary: Get current user profile
      description: Get authenticated user's profile information
      operationId: getCurrentUser
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'

  # ============================================================================
  # USER MANAGEMENT ENDPOINTS
  # ============================================================================

  /users:
    get:
      tags:
        - Users
      summary: Get users list
      description: Get paginated list of users with filtering options
      operationId: getUsers
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          description: Page number
        - name: size
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Page size
        - name: role
          in: query
          schema:
            type: string
            enum: [admin, manager, analyst, viewer]
          description: Filter by role
        - name: department
          in: query
          schema:
            type: string
          description: Filter by department
        - name: is_active
          in: query
          schema:
            type: boolean
          description: Filter by active status
        - name: search
          in: query
          schema:
            type: string
          description: Search by name or email
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'

    post:
      tags:
        - Users
      summary: Create user
      description: Create a new user account
      operationId: createUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /users/{user_id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Get specific user information
      operationId: getUserById
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Users
      summary: Update user
      description: Update user information
      operationId: updateUser
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdate'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'

    delete:
      tags:
        - Users
      summary: Delete user
      description: Delete user account
      operationId: deleteUser
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      responses:
        '204':
          description: User deleted successfully

  # ============================================================================
  # COMPLIANCE MANAGEMENT ENDPOINTS
  # ============================================================================

  /compliance/tasks:
    get:
      tags:
        - Compliance
      summary: Get compliance tasks
      description: Get list of compliance tasks with filtering and pagination
      operationId: getComplianceTasks
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, in_progress, completed, overdue]
        - name: priority
          in: query
          schema:
            type: string
            enum: [low, medium, high, critical]
        - name: assigned_to
          in: query
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Compliance tasks retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplianceTaskListResponse'

    post:
      tags:
        - Compliance
      summary: Create compliance task
      description: Create a new compliance task
      operationId: createComplianceTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplianceTaskCreate'
      responses:
        '201':
          description: Compliance task created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplianceTaskResponse'

  /compliance/reports:
    get:
      tags:
        - Compliance
      summary: Get compliance reports
      description: Get list of compliance reports
      operationId: getComplianceReports
      parameters:
        - name: report_type
          in: query
          schema:
            type: string
            enum: [aml, kyc, regulatory, risk]
        - name: date_from
          in: query
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Compliance reports retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplianceReportListResponse'

    post:
      tags:
        - Compliance
      summary: Generate compliance report
      description: Generate a new compliance report
      operationId: generateComplianceReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplianceReportRequest'
      responses:
        '202':
          description: Report generation started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportGenerationResponse'

  # ============================================================================
  # TRAINING PORTAL ENDPOINTS
  # ============================================================================

  /training/modules:
    get:
      tags:
        - Training Portal
      summary: Get training modules
      description: Get list of available training modules
      operationId: getTrainingModules
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: category
          in: query
          schema:
            type: string
        - name: difficulty_level
          in: query
          schema:
            type: string
            enum: [beginner, intermediate, advanced]
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Training modules retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrainingModuleListResponse'

    post:
      tags:
        - Training Portal
      summary: Create training module
      description: Create a new training module
      operationId: createTrainingModule
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrainingModuleCreate'
      responses:
        '201':
          description: Training module created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrainingModuleResponse'

  /training/modules/{module_id}:
    get:
      tags:
        - Training Portal
      summary: Get training module
      description: Get specific training module details
      operationId: getTrainingModule
      parameters:
        - name: module_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Training module retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrainingModuleResponse'

  /training/progress:
    get:
      tags:
        - Training Portal
      summary: Get user training progress
      description: Get current user's training progress
      operationId: getTrainingProgress
      responses:
        '200':
          description: Training progress retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrainingProgressResponse'

  # ============================================================================
  # DASHBOARD & ANALYTICS ENDPOINTS
  # ============================================================================

  /dashboard/metrics:
    get:
      tags:
        - Dashboard
      summary: Get dashboard metrics
      description: Get key performance indicators and metrics for dashboard
      operationId: getDashboardMetrics
      responses:
        '200':
          description: Dashboard metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardMetricsResponse'

  /dashboard/alerts:
    get:
      tags:
        - Dashboard
      summary: Get recent alerts
      description: Get recent system alerts and notifications
      operationId: getRecentAlerts
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
      responses:
        '200':
          description: Recent alerts retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertListResponse'

  # ============================================================================
  # EXTERNAL DATA INTEGRATION ENDPOINTS
  # ============================================================================
  /external-data/screen-entity:
    post:
      tags:
        - External Data Integration
      summary: Screen entity against external data sources
      description: |
        Screen an entity against multiple external data sources including sanctions lists,
        market data providers, and credit bureaus.
      operationId: screenEntity
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EntityScreeningRequest'
            examples:
              corporation_screening:
                summary: Corporation screening example
                value:
                  entity_data:
                    name: "Acme Corporation Ltd"
                    entity_type: "corporation"
                    address: "123 Business Street, London, UK"
                    business_registration: "UK12345678"
                  screening_type: "comprehensive"
                  providers: ["ofac", "eu_sanctions", "un_sanctions", "refinitiv"]
      responses:
        '200':
          description: Screening completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EntityScreeningResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
        '500':
          $ref: '#/components/responses/InternalError'

  /external-data/update-sources:
    post:
      tags:
        - External Data Integration
      summary: Update external data sources
      description: Trigger update of external data sources with fresh data
      operationId: updateDataSources
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                providers:
                  type: array
                  items:
                    type: string
                    enum: [ofac, eu_sanctions, un_sanctions, refinitiv, experian]
                  description: List of providers to update
                force_update:
                  type: boolean
                  default: false
                  description: Force update even if data is fresh
      responses:
        '200':
          description: Data sources updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataUpdateResponse'

  # GRC Integration Endpoints
  /integrations/grc/sync-risks:
    post:
      tags:
        - GRC Integration
      summary: Sync risk registers from GRC systems
      description: |
        Synchronize risk registers from connected GRC systems including
        RSA Archer, ServiceNow GRC, and MetricStream.
      operationId: syncRiskRegisters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                system_types:
                  type: array
                  items:
                    type: string
                    enum: [archer, servicenow, metricstream]
                  description: GRC systems to sync from
                sync_mode:
                  type: string
                  enum: [incremental, full]
                  default: incremental
                  description: Synchronization mode
                date_filter:
                  type: string
                  format: date-time
                  description: Only sync records modified after this date
      responses:
        '200':
          description: Risk sync completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RiskSyncResponse'

  /integrations/grc/push-updates:
    post:
      tags:
        - GRC Integration
      summary: Push compliance updates to GRC systems
      description: Push compliance findings and updates to connected GRC systems
      operationId: pushComplianceUpdates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                updates:
                  type: array
                  items:
                    $ref: '#/components/schemas/ComplianceUpdate'
                target_systems:
                  type: array
                  items:
                    type: string
                    enum: [archer, servicenow, metricstream]
      responses:
        '200':
          description: Updates pushed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePushResponse'

  # Notification Endpoints
  /notifications/send:
    post:
      tags:
        - Notifications
      summary: Send notification
      description: Send a single notification using specified template and channels
      operationId: sendNotification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationRequest'
            examples:
              risk_alert:
                summary: Risk alert notification
                value:
                  template_name: "high_risk_alert"
                  recipients: ["<EMAIL>", "<EMAIL>"]
                  context:
                    risk_title: "Operational Risk - System Downtime"
                    risk_level: "HIGH"
                    due_date: "2024-02-15"
                    owner: "IT Operations"
                  priority: "high"
                  channels: ["email", "webhook"]
      responses:
        '200':
          description: Notification sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationResponse'

  /notifications/send-bulk:
    post:
      tags:
        - Notifications
      summary: Send bulk notifications
      description: Send multiple notifications in batch for improved performance
      operationId: sendBulkNotifications
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                notifications:
                  type: array
                  items:
                    $ref: '#/components/schemas/NotificationRequest'
                  maxItems: 1000
                  description: Array of notifications to send (max 1000)
                batch_options:
                  type: object
                  properties:
                    batch_size:
                      type: integer
                      default: 100
                      minimum: 1
                      maximum: 500
                    max_concurrent_batches:
                      type: integer
                      default: 5
                      minimum: 1
                      maximum: 20
      responses:
        '200':
          description: Bulk notifications processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkNotificationResponse'

  /notifications/templates:
    get:
      tags:
        - Notifications
      summary: List notification templates
      description: Get list of available notification templates
      operationId: listNotificationTemplates
      parameters:
        - name: template_type
          in: query
          schema:
            type: string
            enum: [email, sms, webhook]
          description: Filter by template type
        - name: language
          in: query
          schema:
            type: string
            default: en
          description: Template language
      responses:
        '200':
          description: Templates retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  templates:
                    type: array
                    items:
                      $ref: '#/components/schemas/NotificationTemplate'

    post:
      tags:
        - Notifications
      summary: Create notification template
      description: Create a new notification template
      operationId: createNotificationTemplate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationTemplateCreate'
      responses:
        '201':
          description: Template created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationTemplate'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # ============================================================================
    # AUTHENTICATION SCHEMAS
    # ============================================================================

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          description: User email address
          example: "<EMAIL>"
        password:
          type: string
          format: password
          description: User password
          example: "admin123"
        remember_me:
          type: boolean
          default: false
          description: Extended session duration

    LoginResponse:
      type: object
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: JWT refresh token
        token_type:
          type: string
          example: "bearer"
        expires_in:
          type: integer
          description: Token expiration time in seconds
        user:
          $ref: '#/components/schemas/UserProfile'

    LogoutResponse:
      type: object
      properties:
        message:
          type: string
          example: "Successfully logged out"

    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
          description: New JWT access token
        token_type:
          type: string
          example: "bearer"
        expires_in:
          type: integer
          description: Token expiration time in seconds

    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
        tenant_id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        full_name:
          type: string
        role:
          type: string
          enum: [admin, manager, analyst, viewer]
        department:
          type: string
        permissions:
          type: array
          items:
            type: string
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_login:
          type: string
          format: date-time

    # ============================================================================
    # USER MANAGEMENT SCHEMAS
    # ============================================================================

    UserCreate:
      type: object
      required:
        - email
        - full_name
        - role
        - department
      properties:
        email:
          type: string
          format: email
        full_name:
          type: string
          minLength: 2
          maxLength: 100
        role:
          type: string
          enum: [admin, manager, analyst, viewer]
        department:
          type: string
        password:
          type: string
          format: password
          minLength: 8
        permissions:
          type: array
          items:
            type: string

    UserUpdate:
      type: object
      properties:
        full_name:
          type: string
          minLength: 2
          maxLength: 100
        role:
          type: string
          enum: [admin, manager, analyst, viewer]
        department:
          type: string
        is_active:
          type: boolean
        permissions:
          type: array
          items:
            type: string

    UserResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        tenant_id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        full_name:
          type: string
        role:
          type: string
          enum: [admin, manager, analyst, viewer]
        department:
          type: string
        permissions:
          type: array
          items:
            type: string
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_login:
          type: string
          format: date-time

    UserListResponse:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    # ============================================================================
    # COMPLIANCE SCHEMAS
    # ============================================================================

    ComplianceTaskCreate:
      type: object
      required:
        - title
        - description
        - priority
        - due_date
      properties:
        title:
          type: string
          minLength: 5
          maxLength: 200
        description:
          type: string
          maxLength: 2000
        priority:
          type: string
          enum: [low, medium, high, critical]
        due_date:
          type: string
          format: date
        assigned_to:
          type: string
          format: uuid
        category:
          type: string
        tags:
          type: array
          items:
            type: string

    ComplianceTaskResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        status:
          type: string
          enum: [pending, in_progress, completed, overdue]
        priority:
          type: string
          enum: [low, medium, high, critical]
        due_date:
          type: string
          format: date
        assigned_to:
          type: string
          format: uuid
        assigned_to_name:
          type: string
        category:
          type: string
        tags:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time

    ComplianceTaskListResponse:
      type: object
      properties:
        tasks:
          type: array
          items:
            $ref: '#/components/schemas/ComplianceTaskResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    ComplianceReportRequest:
      type: object
      required:
        - report_type
        - date_from
        - date_to
      properties:
        report_type:
          type: string
          enum: [aml, kyc, regulatory, risk, comprehensive]
        date_from:
          type: string
          format: date
        date_to:
          type: string
          format: date
        filters:
          type: object
          additionalProperties: true
        format:
          type: string
          enum: [pdf, excel, csv]
          default: pdf

    ComplianceReportListResponse:
      type: object
      properties:
        reports:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              name:
                type: string
              type:
                type: string
              status:
                type: string
                enum: [generating, completed, failed]
              created_at:
                type: string
                format: date-time
              file_size:
                type: integer
              download_url:
                type: string

    ReportGenerationResponse:
      type: object
      properties:
        report_id:
          type: string
          format: uuid
        status:
          type: string
          example: "generating"
        estimated_completion:
          type: string
          format: date-time

    # ============================================================================
    # TRAINING PORTAL SCHEMAS
    # ============================================================================

    TrainingModuleCreate:
      type: object
      required:
        - module_code
        - title
        - category
      properties:
        module_code:
          type: string
          minLength: 2
          maxLength: 50
        title:
          type: string
          minLength: 2
          maxLength: 200
        description:
          type: string
          maxLength: 1000
        category:
          type: string
        difficulty_level:
          type: string
          enum: [beginner, intermediate, advanced]
          default: beginner
        estimated_duration_minutes:
          type: integer
          minimum: 1
          maximum: 1440
          default: 60
        prerequisites:
          type: array
          items:
            type: string
        learning_objectives:
          type: array
          items:
            type: string
        content_type:
          type: string
          default: interactive

    TrainingModuleResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
        module_code:
          type: string
        title:
          type: string
        description:
          type: string
        category:
          type: string
        difficulty_level:
          type: string
          enum: [beginner, intermediate, advanced]
        estimated_duration_minutes:
          type: integer
        prerequisites:
          type: array
          items:
            type: string
        learning_objectives:
          type: array
          items:
            type: string
        content_type:
          type: string
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    TrainingModuleListResponse:
      type: object
      properties:
        modules:
          type: array
          items:
            $ref: '#/components/schemas/TrainingModuleResponse'
        pagination:
          $ref: '#/components/schemas/PaginationInfo'

    TrainingProgressResponse:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
        overall_progress:
          type: number
          minimum: 0
          maximum: 100
        completed_modules:
          type: integer
        total_modules:
          type: integer
        certificates_earned:
          type: integer
        average_score:
          type: number
          minimum: 0
          maximum: 100
        module_progress:
          type: array
          items:
            type: object
            properties:
              module_id:
                type: string
                format: uuid
              module_title:
                type: string
              progress:
                type: number
                minimum: 0
                maximum: 100
              status:
                type: string
                enum: [not_started, in_progress, completed]
              score:
                type: number
              completed_at:
                type: string
                format: date-time

    # ============================================================================
    # DASHBOARD SCHEMAS
    # ============================================================================

    DashboardMetricsResponse:
      type: object
      properties:
        compliance_score:
          type: number
          minimum: 0
          maximum: 100
        active_alerts:
          type: integer
        completed_training:
          type: number
          minimum: 0
          maximum: 100
        system_health:
          type: number
          minimum: 0
          maximum: 100
        total_users:
          type: integer
        pending_tasks:
          type: integer
        recent_reports:
          type: integer
        risk_score:
          type: number
          minimum: 0
          maximum: 100

    AlertListResponse:
      type: object
      properties:
        alerts:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                format: uuid
              type:
                type: string
                enum: [info, warning, error, success]
              title:
                type: string
              message:
                type: string
              severity:
                type: string
                enum: [low, medium, high, critical]
              created_at:
                type: string
                format: date-time
              is_read:
                type: boolean

    # ============================================================================
    # COMMON SCHEMAS
    # ============================================================================

    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
        size:
          type: integer
          minimum: 1
        total_items:
          type: integer
          minimum: 0
        total_pages:
          type: integer
          minimum: 0
        has_next:
          type: boolean
        has_previous:
          type: boolean

    EntityScreeningRequest:
      type: object
      required:
        - entity_data
        - screening_type
      properties:
        entity_data:
          $ref: '#/components/schemas/EntityData'
        screening_type:
          type: string
          enum: [basic, comprehensive, custom]
          description: Type of screening to perform
        providers:
          type: array
          items:
            type: string
            enum: [ofac, eu_sanctions, un_sanctions, refinitiv, experian]
          description: Specific providers to use (optional)
        screening_options:
          type: object
          properties:
            include_historical:
              type: boolean
              default: false
            fuzzy_matching:
              type: boolean
              default: true
            match_threshold:
              type: number
              minimum: 0.0
              maximum: 1.0
              default: 0.8

    EntityData:
      type: object
      required:
        - name
        - entity_type
      properties:
        name:
          type: string
          description: Entity name
          example: "Acme Corporation Ltd"
        entity_type:
          type: string
          enum: [person, corporation, organization, vessel, aircraft]
          description: Type of entity
        address:
          type: string
          description: Entity address
          example: "123 Business Street, London, UK"
        date_of_birth:
          type: string
          format: date
          description: Date of birth (for persons)
        business_registration:
          type: string
          description: Business registration number
        passport_number:
          type: string
          description: Passport number (for persons)
        ticker_symbol:
          type: string
          description: Stock ticker symbol
        isin:
          type: string
          description: International Securities Identification Number
        lei:
          type: string
          description: Legal Entity Identifier

    EntityScreeningResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        entity_name:
          type: string
        screening_id:
          type: string
          format: uuid
        screening_date:
          type: string
          format: date-time
        total_providers:
          type: integer
        successful_screenings:
          type: integer
        failed_screenings:
          type: integer
        total_matches:
          type: integer
        overall_risk_score:
          type: number
          minimum: 0.0
          maximum: 1.0
        screening_result:
          type: string
          enum: [clear, possible_match, potential_match, hit]
        provider_results:
          type: array
          items:
            $ref: '#/components/schemas/ProviderScreeningResult'

    ProviderScreeningResult:
      type: object
      properties:
        provider:
          type: string
        status:
          type: string
          enum: [success, error, timeout]
        screening_date:
          type: string
          format: date-time
        screening_id:
          type: string
        matches:
          type: array
          items:
            $ref: '#/components/schemas/ScreeningMatch'
        data_version:
          type: string
        response_time_ms:
          type: integer

    ScreeningMatch:
      type: object
      properties:
        match_id:
          type: string
        match_strength:
          type: number
          minimum: 0.0
          maximum: 1.0
        matched_name:
          type: string
        list_name:
          type: string
        match_type:
          type: string
          enum: [exact, fuzzy, phonetic, alias]
        additional_info:
          type: object
          additionalProperties: true

    NotificationRequest:
      type: object
      required:
        - template_name
        - recipients
        - context
        - priority
        - channels
      properties:
        template_name:
          type: string
          description: Name of the notification template to use
        recipients:
          type: array
          items:
            type: string
          description: List of recipient email addresses or phone numbers
        context:
          type: object
          additionalProperties: true
          description: Template context variables
        priority:
          type: string
          enum: [low, normal, high, urgent]
          description: Notification priority
        channels:
          type: array
          items:
            type: string
            enum: [email, sms, webhook]
          description: Delivery channels to use
        delivery_options:
          type: object
          properties:
            schedule_time:
              type: string
              format: date-time
              description: Schedule notification for future delivery
            retry_attempts:
              type: integer
              minimum: 0
              maximum: 5
              default: 3

    NotificationResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        notification_id:
          type: string
          format: uuid
        delivery_status:
          type: object
          additionalProperties:
            type: string
            enum: [sent, failed, pending, scheduled]
        delivery_timestamp:
          type: string
          format: date-time
        error_details:
          type: array
          items:
            type: object
            properties:
              channel:
                type: string
              error:
                type: string

    BulkNotificationResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        total_notifications:
          type: integer
        successful_deliveries:
          type: integer
        failed_deliveries:
          type: integer
        processing_time_ms:
          type: integer
        batch_results:
          type: array
          items:
            type: object
            properties:
              batch_id:
                type: string
              notifications_processed:
                type: integer
              successful_deliveries:
                type: integer
              failed_deliveries:
                type: integer

    NotificationTemplate:
      type: object
      properties:
        template_id:
          type: string
          format: uuid
        name:
          type: string
        template_type:
          type: string
          enum: [email, sms, webhook]
        language:
          type: string
        subject:
          type: string
        content:
          type: string
        variables:
          type: array
          items:
            type: string
        created_date:
          type: string
          format: date-time
        last_modified:
          type: string
          format: date-time

    NotificationTemplateCreate:
      type: object
      required:
        - name
        - template_type
        - subject
        - content
      properties:
        name:
          type: string
        template_type:
          type: string
          enum: [email, sms, webhook]
        language:
          type: string
          default: en
        subject:
          type: string
        content:
          type: string
        variables:
          type: array
          items:
            type: string

    DataUpdateResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        update_timestamp:
          type: string
          format: date-time
        update_results:
          type: array
          items:
            type: object
            properties:
              provider:
                type: string
              status:
                type: string
                enum: [success, error, skipped]
              total_processed:
                type: integer
              new_records:
                type: integer
              updated_records:
                type: integer
              removed_records:
                type: integer
              data_version:
                type: string
              error_message:
                type: string

    RiskSyncResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        sync_timestamp:
          type: string
          format: date-time
        total_risks_processed:
          type: integer
        total_risks_created:
          type: integer
        total_risks_updated:
          type: integer
        total_conflicts:
          type: integer
        system_results:
          type: array
          items:
            type: object
            properties:
              system_name:
                type: string
              system_type:
                type: string
              total_processed:
                type: integer
              created:
                type: integer
              updated:
                type: integer
              conflicts:
                type: integer
              sync_duration_ms:
                type: integer

    ComplianceUpdate:
      type: object
      required:
        - type
        - title
        - description
      properties:
        type:
          type: string
          enum: [risk, control, incident, finding]
        title:
          type: string
        description:
          type: string
        risk_level:
          type: string
          enum: [low, medium, high, critical]
        category:
          type: string
        owner:
          type: string
        due_date:
          type: string
          format: date
        external_id:
          type: string
        metadata:
          type: object
          additionalProperties: true

    UpdatePushResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, partial_success, error]
        push_timestamp:
          type: string
          format: date-time
        total_updates:
          type: integer
        successful_pushes:
          type: integer
        failed_pushes:
          type: integer
        system_results:
          type: array
          items:
            type: object
            properties:
              system_name:
                type: string
              system_type:
                type: string
              updates_pushed:
                type: integer
              updates_failed:
                type: integer
              ticket_ids:
                type: array
                items:
                  type: string

    Error:
      type: object
      properties:
        type:
          type: string
          format: uri
        title:
          type: string
        status:
          type: integer
        detail:
          type: string
        instance:
          type: string
          format: uri
        timestamp:
          type: string
          format: date-time
        trace_id:
          type: string

  responses:
    BadRequest:
      description: Bad request - invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            type: "https://regulensai.com/errors/bad-request"
            title: "Bad Request"
            status: 400
            detail: "Invalid entity data: missing required field 'name'"
            instance: "/api/v1/external-data/screen-entity"
            timestamp: "2024-01-29T10:30:00Z"
            trace_id: "abc123def456"

    Unauthorized:
      description: Unauthorized - invalid or missing authentication
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            type: "https://regulensai.com/errors/unauthorized"
            title: "Unauthorized"
            status: 401
            detail: "Invalid or expired authentication token"
            instance: "/api/v1/external-data/screen-entity"
            timestamp: "2024-01-29T10:30:00Z"
            trace_id: "abc123def456"

    RateLimited:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            type: "https://regulensai.com/errors/rate-limited"
            title: "Rate Limit Exceeded"
            status: 429
            detail: "Rate limit of 1000 requests per minute exceeded"
            instance: "/api/v1/external-data/screen-entity"
            timestamp: "2024-01-29T10:30:00Z"
            trace_id: "abc123def456"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            type: "https://regulens.ai/errors/not-found"
            title: "Not Found"
            status: 404
            detail: "The requested resource was not found"
            instance: "/api/v1/users/123"
            timestamp: "2024-01-29T10:30:00Z"
            trace_id: "abc123def456"

    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            type: "https://regulens.ai/errors/internal-error"
            title: "Internal Server Error"
            status: 500
            detail: "An unexpected error occurred while processing the request"
            instance: "/api/v1/external-data/screen-entity"
            timestamp: "2024-01-29T10:30:00Z"
            trace_id: "abc123def456"

tags:
  - name: System
    description: System health and status endpoints
  - name: Authentication
    description: User authentication and authorization operations
  - name: Users
    description: User management and profile operations
  - name: Compliance
    description: Compliance task and report management
  - name: Training Portal
    description: Training module and progress management
  - name: Dashboard
    description: Dashboard metrics and analytics
  - name: External Data Integration
    description: Operations for screening entities against external data sources
  - name: GRC Integration
    description: Operations for integrating with GRC systems
  - name: Notifications
    description: Operations for sending notifications and managing templates
