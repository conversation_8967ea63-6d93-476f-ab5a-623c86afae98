# RegulensAI Web-Based Deployment Runbooks Configuration
# This file defines the deployment procedures that are integrated into the web UI

version: "1.0"
metadata:
  name: "RegulensAI Deployment Runbooks"
  description: "Interactive deployment guides integrated into the web interface"
  lastUpdated: "2024-01-29"
  maintainer: "DevOps Team <<EMAIL>>"

# Environment configurations
environments:
  staging:
    name: "Staging Environment"
    description: "Pre-production testing environment"
    database_url_env: "STAGING_DB_URL"
    kubernetes_namespace: "regulensai-staging"
    helm_values_file: "values-staging.yaml"
    monitoring_enabled: true
    backup_required: true
    approval_required: false
    
  production:
    name: "Production Environment"
    description: "Live production environment"
    database_url_env: "PROD_DB_URL"
    kubernetes_namespace: "regulensai-prod"
    helm_values_file: "values-production.yaml"
    monitoring_enabled: true
    backup_required: true
    approval_required: true
    blue_green_deployment: true

# Deployment workflow definitions
workflows:
  staging_deployment:
    name: "Staging Deployment"
    description: "Deploy to staging environment with full validation"
    environment: "staging"
    steps:
      - id: "pre_validation"
        name: "Pre-deployment Validation"
        description: "Verify prerequisites and system readiness"
        required: true
        tasks:
          - id: "security_scan"
            name: "Security Scan"
            description: "Run security scan on container images"
            command: "docker run --rm aquasec/trivy image regulensai:${VERSION}"
            required: true
          - id: "k8s_access"
            name: "Kubernetes Access"
            description: "Verify Kubernetes cluster access"
            command: "kubectl cluster-info"
            required: true
          - id: "db_backup"
            name: "Database Backup"
            description: "Create database backup before deployment"
            command: "pg_dump ${STAGING_DB_URL} > backup_$(date +%Y%m%d_%H%M%S).sql"
            required: true
            
      - id: "database_migration"
        name: "Database Migration"
        description: "Apply database schema changes"
        required: true
        tasks:
          - id: "migration_dry_run"
            name: "Migration Dry Run"
            description: "Preview migration changes"
            command: "python core_infra/database/migrate.py --database-url ${STAGING_DB_URL} --dry-run"
            required: true
          - id: "apply_migrations"
            name: "Apply Migrations"
            description: "Apply database migrations"
            command: "python core_infra/database/migrate.py --database-url ${STAGING_DB_URL}"
            required: true
            
      - id: "application_deployment"
        name: "Application Deployment"
        description: "Deploy application services"
        required: true
        tasks:
          - id: "helm_deploy"
            name: "Helm Deployment"
            description: "Deploy using Helm charts"
            command: "helm upgrade --install regulensai ./helm/regulensai --namespace regulensai-staging --values ./helm/regulensai/values-staging.yaml --set image.tag=${VERSION}"
            required: true
            
      - id: "post_validation"
        name: "Post-deployment Validation"
        description: "Verify deployment success"
        required: true
        tasks:
          - id: "health_check"
            name: "Health Check"
            description: "Test API health endpoints"
            command: "curl -f https://staging.regulens.ai/api/v1/health"
            required: true
          - id: "auth_test"
            name: "Authentication Test"
            description: "Test authentication flow"
            command: "curl -X POST https://staging.regulens.ai/api/v1/auth/login -H 'Content-Type: application/json' -d '{\"email\": \"<EMAIL>\", \"password\": \"admin123\"}'"
            required: false

  production_deployment:
    name: "Production Deployment"
    description: "Blue-green deployment to production with comprehensive validation"
    environment: "production"
    approval_required: true
    steps:
      - id: "security_validation"
        name: "Security Validation"
        description: "Comprehensive security and compliance checks"
        required: true
        tasks:
          - id: "vulnerability_scan"
            name: "Vulnerability Scan"
            description: "Deep security scan of all components"
            command: "docker run --rm aquasec/trivy image regulensai:${VERSION} --severity HIGH,CRITICAL"
            required: true
          - id: "compliance_check"
            name: "Compliance Check"
            description: "Verify SOC 2 compliance requirements"
            command: "kubectl run compliance-check --rm -i --tty --restart=Never --image=compliance-scanner:latest"
            required: true
            
      - id: "database_operations"
        name: "Database Operations"
        description: "Production database migration with safety checks"
        required: true
        tasks:
          - id: "create_restore_point"
            name: "Create Restore Point"
            description: "Create database restore point"
            command: "psql ${PROD_DB_URL} -c \"SELECT pg_create_restore_point('pre_migration_$(date +%Y%m%d_%H%M%S)');\""
            required: true
          - id: "migration_validation"
            name: "Migration Validation"
            description: "Validate migration scripts"
            command: "python core_infra/database/migrate.py --database-url ${PROD_DB_URL} --validate-only"
            required: true
          - id: "apply_migrations"
            name: "Apply Migrations"
            description: "Apply database migrations with monitoring"
            command: "python core_infra/database/migrate.py --database-url ${PROD_DB_URL} --monitor-performance"
            required: true
            
      - id: "blue_green_deployment"
        name: "Blue-Green Deployment"
        description: "Deploy new version alongside current production"
        required: true
        tasks:
          - id: "deploy_green"
            name: "Deploy Green Environment"
            description: "Deploy new version to green environment"
            command: "helm install regulensai-green ./helm/regulensai --namespace regulensai-prod --values ./helm/regulensai/values-production.yaml --set image.tag=${VERSION} --set deployment.color=green --set service.enabled=false"
            required: true
          - id: "validate_green"
            name: "Validate Green Environment"
            description: "Comprehensive validation of green environment"
            command: "kubectl exec -n regulensai-prod deployment/regulensai-green-api -- python -m pytest tests/health/ -v"
            required: true
          - id: "traffic_switch"
            name: "Traffic Switch"
            description: "Gradually switch traffic to green environment"
            command: "kubectl patch service regulensai-api -n regulensai-prod -p '{\"spec\":{\"selector\":{\"app\":\"regulensai-green-api\"}}}'"
            required: true

# Troubleshooting procedures
troubleshooting:
  database_connection_failed:
    title: "Database Connection Failed"
    severity: "critical"
    symptoms:
      - "Application pods failing to start"
      - "Connection timeout errors in logs"
      - "Health check endpoints returning 500 errors"
    solutions:
      - title: "Verify Database Connectivity"
        steps:
          - "Check database server status: kubectl get pods -n database"
          - "Test network connectivity: nc -zv database-host 5432"
          - "Verify credentials: psql $DATABASE_URL -c 'SELECT 1;'"
      - title: "Check Connection Pool"
        steps:
          - "Review pool settings: kubectl logs deployment/regulensai-api | grep 'connection pool'"
          - "Monitor connections: psql $DATABASE_URL -c 'SELECT count(*), state FROM pg_stat_activity GROUP BY state;'"

  migration_failed:
    title: "Database Migration Failed"
    severity: "critical"
    symptoms:
      - "Migration script errors in logs"
      - "Schema version mismatch"
      - "Application startup failures"
    solutions:
      - title: "Rollback and Retry"
        steps:
          - "Check migration status: python migrate.py --status"
          - "Rollback failed migration: python migrate.py --rollback migration_name"
          - "Fix migration script and retry: python migrate.py --dry-run && python migrate.py"

  high_api_latency:
    title: "High API Response Times"
    severity: "warning"
    symptoms:
      - "Slow response times (>2 seconds)"
      - "Timeout errors from clients"
      - "High CPU usage on API pods"
    solutions:
      - title: "Database Query Optimization"
        steps:
          - "Identify slow queries: psql $DATABASE_URL -c 'SELECT query, total_time FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;'"
          - "Check missing indexes: EXPLAIN ANALYZE slow_query"
      - title: "Scale Resources"
        steps:
          - "Check resource usage: kubectl top pods -n regulensai-prod"
          - "Scale horizontally: kubectl scale deployment regulensai-api --replicas=5"

# Monitoring and alerting configuration
monitoring:
  components:
    prometheus:
      enabled: true
      setup_commands:
        - "helm repo add prometheus-community https://prometheus-community.github.io/helm-charts"
        - "helm install prometheus prometheus-community/kube-prometheus-stack --namespace monitoring --create-namespace"
    grafana:
      enabled: true
      setup_commands:
        - "kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring"
    alertmanager:
      enabled: true
      setup_commands:
        - "kubectl apply -f monitoring/alertmanager-config.yaml"

  alert_rules:
    - name: "High CPU Usage"
      severity: "warning"
      condition: "cpu_usage > 80%"
      enabled: true
    - name: "Database Connection Failures"
      severity: "critical"
      condition: "db_connection_failures > 5"
      enabled: true
    - name: "API Response Time"
      severity: "warning"
      condition: "api_response_time > 2s"
      enabled: true

# Backup and disaster recovery
backup:
  database:
    frequency: "daily"
    retention: "30 days"
    commands:
      create: "pg_dump ${DATABASE_URL} | gzip > backup_$(date +%Y%m%d).sql.gz"
      restore: "gunzip -c backup_file.sql.gz | psql ${DATABASE_URL}"
  
  application:
    frequency: "weekly"
    retention: "12 weeks"
    commands:
      create: "kubectl create backup regulensai-backup --namespace regulensai-prod"
      restore: "kubectl restore regulensai-backup --namespace regulensai-prod"

# Security and compliance
security:
  required_scans:
    - "Container vulnerability scan"
    - "Kubernetes security benchmark"
    - "Network policy validation"
    - "RBAC configuration review"
  
  compliance_checks:
    - "SOC 2 Type II requirements"
    - "Data encryption validation"
    - "Access control verification"
    - "Audit logging confirmation"

# Integration settings
integration:
  api_endpoints:
    status: "/api/v1/operations/status"
    migrations: "/api/v1/operations/database/migrations"
    deploy: "/api/v1/operations/deploy"
    backup: "/api/v1/operations/database/backup"
  
  permissions:
    read: "operations.read"
    deploy_staging: "operations.deploy.staging"
    deploy_production: "operations.deploy.production"
    database_write: "operations.database.write"
