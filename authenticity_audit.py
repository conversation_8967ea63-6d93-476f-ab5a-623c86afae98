#!/usr/bin/env python3
"""
Authenticity Audit for RegulensAI API
Tests all endpoints against actual production database to verify real operations.
"""

import asyncio
import json
import requests
import time
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core_infra.database.connection import get_database

API_BASE_URL = "http://localhost:8002"

class AuthenticityAuditor:
    def __init__(self):
        self.results = {
            'total_endpoints_tested': 0,
            'real_database_operations': 0,
            'hardcoded_responses': 0,
            'mock_implementations': [],
            'database_connected': False,
            'fake_responses_found': []
        }
        
    async def test_database_connectivity(self) -> bool:
        """Test direct database connectivity."""
        try:
            async with get_database() as db:
                # Test basic database operations
                result = await db.fetchval("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")
                print(f"✅ Database connected: {result} tables found")
                self.results['database_connected'] = True
                
                # Test specific RegulensAI tables
                tables_to_check = [
                    'tenants', 'users', 'customers', 'transactions', 
                    'audit_logs', 'compliance_rules', 'screening_results'
                ]
                
                for table in tables_to_check:
                    try:
                        count = await db.fetchval(f"SELECT COUNT(*) FROM {table}")
                        print(f"  📋 Table '{table}': {count} records")
                    except Exception as e:
                        print(f"  ❌ Table '{table}': Error - {e}")
                
                return True
                
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def test_endpoint_for_hardcoded_responses(self, endpoint: str, method: str = "GET") -> Dict[str, Any]:
        """Test endpoint and analyze response for hardcoded data patterns."""
        try:
            self.results['total_endpoints_tested'] += 1
            
            if method == "GET":
                response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=10)
            elif method == "POST":
                response = requests.post(f"{API_BASE_URL}{endpoint}", json={}, timeout=10)
            else:
                response = requests.request(method, f"{API_BASE_URL}{endpoint}", timeout=10)
            
            # Analyze response for hardcoded patterns
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Check for suspicious hardcoded patterns
                    data_str = json.dumps(data).lower()
                    
                    hardcoded_indicators = [
                        'mock', 'test', 'example', 'placeholder', 'dummy',
                        'fake', 'sample', 'demo', 'hardcoded', 'static'
                    ]
                    
                    suspicious_patterns = [
                        '"id": "system-1"',
                        '"id": "mock-',
                        '"system_name": "archer grc"',
                        '"accuracy": 0.94',
                        '"predictions_today": 1247',
                        '"total_monitored_entities": 1247'
                    ]
                    
                    found_indicators = []
                    for indicator in hardcoded_indicators:
                        if indicator in data_str:
                            found_indicators.append(indicator)
                    
                    found_patterns = []
                    for pattern in suspicious_patterns:
                        if pattern in data_str:
                            found_patterns.append(pattern)
                    
                    if found_indicators or found_patterns:
                        self.results['hardcoded_responses'] += 1
                        self.results['fake_responses_found'].append({
                            'endpoint': endpoint,
                            'method': method,
                            'indicators': found_indicators,
                            'patterns': found_patterns,
                            'response_sample': str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                        })
                        print(f"  ⚠️ {method} {endpoint}: Suspicious hardcoded response detected")
                        return {'status': 'suspicious', 'indicators': found_indicators, 'patterns': found_patterns}
                    else:
                        self.results['real_database_operations'] += 1
                        print(f"  ✅ {method} {endpoint}: Appears to be real data")
                        return {'status': 'authentic'}
                        
                except json.JSONDecodeError:
                    print(f"  ✅ {method} {endpoint}: Non-JSON response (likely authentic)")
                    return {'status': 'authentic'}
            
            elif response.status_code in [401, 403]:
                print(f"  ✅ {method} {endpoint}: Authentication required (expected)")
                return {'status': 'authentic'}
            elif response.status_code == 422:
                print(f"  ✅ {method} {endpoint}: Validation error (expected)")
                return {'status': 'authentic'}
            else:
                print(f"  ⚠️ {method} {endpoint}: Status {response.status_code}")
                return {'status': 'unknown', 'status_code': response.status_code}
                
        except Exception as e:
            print(f"  ❌ {method} {endpoint}: Exception - {e}")
            return {'status': 'error', 'error': str(e)}

    async def run_authenticity_audit(self):
        """Run comprehensive authenticity audit."""
        print("=" * 80)
        print("🔍 AUTHENTICITY AUDIT FOR REGULENSAI API")
        print("=" * 80)
        print(f"Testing API at: {API_BASE_URL}")
        print(f"Started at: {datetime.now().isoformat()}")
        
        # Test database connectivity first
        print("\n📋 Testing Direct Database Connectivity:")
        db_connected = await self.test_database_connectivity()
        
        if not db_connected:
            print("❌ Cannot proceed with authenticity audit - database not accessible")
            return False
        
        # Test public endpoints that might have hardcoded responses
        print("\n📋 Testing Public Endpoints for Hardcoded Responses:")
        public_endpoints = [
            ("/v1/health", "GET"),
            ("/v1/info", "GET"),
        ]
        
        for endpoint, method in public_endpoints:
            self.test_endpoint_for_hardcoded_responses(endpoint, method)
        
        # Test endpoints that are known to have mock implementations
        print("\n📋 Testing Endpoints Known to Have Mock Implementations:")
        mock_endpoints = [
            ("/v1/integrations/systems", "GET"),
            ("/v1/operations/monitoring/alerts", "GET"),
            ("/v1/ai/models/status", "GET"),
        ]
        
        for endpoint, method in mock_endpoints:
            self.test_endpoint_for_hardcoded_responses(endpoint, method)
        
        # Generate summary
        self.generate_authenticity_summary()
        
        return self.results['hardcoded_responses'] == 0

    def generate_authenticity_summary(self):
        """Generate authenticity audit summary."""
        print("\n" + "=" * 80)
        print("📊 AUTHENTICITY AUDIT SUMMARY")
        print("=" * 80)
        
        total = self.results['total_endpoints_tested']
        real_ops = self.results['real_database_operations']
        hardcoded = self.results['hardcoded_responses']
        
        authenticity_rate = (real_ops / total * 100) if total > 0 else 0
        
        print(f"Total Endpoints Tested: {total}")
        print(f"Real Database Operations: {real_ops}")
        print(f"Hardcoded/Mock Responses: {hardcoded}")
        print(f"Authenticity Rate: {authenticity_rate:.1f}%")
        
        if self.results['database_connected']:
            print("✅ Database Connectivity: CONFIRMED")
        else:
            print("❌ Database Connectivity: FAILED")
        
        if self.results['fake_responses_found']:
            print(f"\n❌ Fake/Hardcoded Responses Found ({len(self.results['fake_responses_found'])}):")
            for fake in self.results['fake_responses_found']:
                print(f"  - {fake['endpoint']}: {fake['indicators']} {fake['patterns']}")
        
        # Overall assessment
        print(f"\n🎯 AUTHENTICITY ASSESSMENT:")
        if authenticity_rate >= 90:
            print("🎉 EXCELLENT: System uses real database operations")
        elif authenticity_rate >= 70:
            print("✅ GOOD: Mostly authentic with some mock implementations")
        elif authenticity_rate >= 50:
            print("⚠️ FAIR: Mixed authentic and mock implementations")
        else:
            print("❌ POOR: Significant use of hardcoded/mock responses")
        
        return authenticity_rate >= 70

async def main():
    """Main audit function."""
    auditor = AuthenticityAuditor()
    success = await auditor.run_authenticity_audit()
    return 0 if success else 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
