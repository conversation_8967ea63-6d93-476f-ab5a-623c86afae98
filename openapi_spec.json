{"openapi": "3.1.0", "info": {"title": "RegulensAI Financial Compliance Platform", "description": "Enterprise-grade financial compliance and regulatory monitoring platform", "version": "1.0.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "Root endpoint", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/health": {"get": {"summary": "Health Check", "description": "Health check endpoint", "operationId": "health_check_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/v1/info": {"get": {"summary": "Platform Info", "description": "Platform information endpoint", "operationId": "platform_info_v1_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InfoResponse"}}}}}}}, "/v1/metrics": {"get": {"summary": "Get Metrics", "description": "Metrics endpoint for Prometheus", "operationId": "get_metrics_v1_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/compliance/status": {"get": {"summary": "Compliance Status", "description": "Get overall compliance status", "operationId": "compliance_status_v1_compliance_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/monitoring/alerts": {"get": {"summary": "<PERSON>erts", "description": "Get active monitoring alerts", "operationId": "get_alerts_v1_monitoring_alerts_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HealthResponse": {"properties": {"status": {"type": "string", "title": "Status"}, "timestamp": {"type": "string", "title": "Timestamp"}, "environment": {"type": "string", "title": "Environment"}, "version": {"type": "string", "title": "Version"}, "services": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Services"}}, "type": "object", "required": ["status", "timestamp", "environment", "version", "services"], "title": "HealthResponse"}, "InfoResponse": {"properties": {"platform_name": {"type": "string", "title": "Platform Name"}, "version": {"type": "string", "title": "Version"}, "environment": {"type": "string", "title": "Environment"}, "features": {"items": {}, "type": "array", "title": "Features"}, "endpoints": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Endpoints"}}, "type": "object", "required": ["platform_name", "version", "environment", "features", "endpoints"], "title": "InfoResponse"}}}}