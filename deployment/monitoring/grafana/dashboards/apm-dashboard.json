{"dashboard": {"id": null, "title": "RegulensAI APM Dashboard", "tags": ["regulensai", "apm", "performance"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "API Response Time", "type": "stat", "targets": [{"expr": "avg(api_request_duration_seconds{job=\"regulensai\"})", "legendFormat": "Avg Response Time"}], "fieldConfig": {"defaults": {"unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 1.0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(api_errors_total{job=\"regulensai\"}[5m])", "legendFormat": "Error Rate"}], "fieldConfig": {"defaults": {"unit": "reqps", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Throughput", "type": "stat", "targets": [{"expr": "rate(api_requests_total{job=\"regulensai\"}[5m])", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Active Users", "type": "stat", "targets": [{"expr": "count(count by (user_id) (increase(user_api_activity{job=\"regulensai\"}[1h])))", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 10}, {"color": "green", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "API Response Time Trends", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(api_request_duration_seconds_bucket{job=\"regulensai\"}[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(api_request_duration_seconds_bucket{job=\"regulensai\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(api_request_duration_seconds_bucket{job=\"regulensai\"}[5m]))", "legendFormat": "99th percentile"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Error Rate by Endpoint", "type": "timeseries", "targets": [{"expr": "rate(api_errors_total{job=\"regulensai\"}[5m]) by (endpoint)", "legendFormat": "{{endpoint}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Database Query Performance", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(database_query_time_bucket{job=\"regulensai\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "avg(database_query_time{job=\"regulensai\"})", "legendFormat": "Average"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Resource Usage", "type": "timeseries", "targets": [{"expr": "resource_cpu_percent{job=\"regulensai\"}", "legendFormat": "CPU %"}, {"expr": "resource_memory_percent{job=\"regulensai\"}", "legendFormat": "Memory %"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Business Metrics - Compliance Processing", "type": "timeseries", "targets": [{"expr": "avg(compliance_processing_times{job=\"regulensai\"}) by (regulation_type)", "legendFormat": "{{regulation_type}}"}], "fieldConfig": {"defaults": {"unit": "s", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 10, "title": "Regulatory Data Ingestion Rate", "type": "timeseries", "targets": [{"expr": "avg(regulatory_data_ingestion_rates{job=\"regulensai\"}) by (source)", "legendFormat": "{{source}}"}], "fieldConfig": {"defaults": {"unit": "rps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 11, "title": "Top Slow Endpoints", "type": "table", "targets": [{"expr": "topk(10, avg by (endpoint) (api_request_duration_seconds{job=\"regulensai\"}))", "format": "table", "instant": true}], "fieldConfig": {"defaults": {"custom": {"displayMode": "table"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 12, "title": "Error Distribution", "type": "piechart", "targets": [{"expr": "sum by (error_type) (api_errors_total{job=\"regulensai\"})", "legendFormat": "{{error_type}}"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "table"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 13, "title": "<PERSON><PERSON>", "type": "timeseries", "targets": [{"expr": "rate(cache_hits_total{job=\"regulensai\"}[5m]) / (rate(cache_hits_total{job=\"regulensai\"}[5m]) + rate(cache_misses_total{job=\"regulensai\"}[5m]))", "legendFormat": "<PERSON><PERSON> Hit Rate"}, {"expr": "avg(cache_operation_time{job=\"regulensai\"})", "legendFormat": "Cache Operation Time"}], "fieldConfig": {"defaults": {"unit": "percent", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}, {"id": 14, "title": "Performance Regression Alerts", "type": "logs", "targets": [{"expr": "{job=\"regulensai\"} |= \"regression\"", "refId": "A"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 48}}], "templating": {"list": [{"name": "environment", "type": "query", "query": "label_values(api_requests_total, environment)", "current": {"value": "production", "text": "production"}}, {"name": "tenant", "type": "query", "query": "label_values(api_requests_total{environment=\"$environment\"}, tenant_id)", "current": {"value": "all", "text": "All"}}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(up{job=\"regulensai\"}[1m])", "titleFormat": "Deployment", "textFormat": "Service restarted"}, {"name": "Performance Regressions", "datasource": "<PERSON>", "expr": "{job=\"regulensai\"} |= \"regression\"", "titleFormat": "Performance Regression", "textFormat": "{{message}}"}]}, "links": [{"title": "RegulensAI Logs", "url": "/d/logs-dashboard", "type": "dashboards"}, {"title": "Infrastructure Monitoring", "url": "/d/infrastructure-dashboard", "type": "dashboards"}]}}