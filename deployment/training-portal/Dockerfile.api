# Multi-stage build for Training Portal API
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILD_ENV=production
ARG APP_VERSION=latest

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt requirements-prod.txt ./

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install -r requirements-prod.txt

# Copy application code
COPY core_infra/ ./core_infra/
COPY alembic/ ./alembic/
COPY alembic.ini ./
COPY main.py ./

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/certificates /app/backups

# Set ownership
RUN chown -R appuser:appuser /app

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/app/.local/bin:$PATH" \
    APP_VERSION=${APP_VERSION}

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Copy from builder stage
COPY --from=builder --chown=appuser:appuser /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder --chown=appuser:appuser /usr/local/bin /usr/local/bin
COPY --from=builder --chown=appuser:appuser /app /app

# Create volume mount points
VOLUME ["/app/logs", "/app/uploads", "/app/certificates"]

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/training/health || exit 1

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]

# Development stage
FROM builder as development

# Install development dependencies
COPY requirements-dev.txt ./
RUN pip install -r requirements-dev.txt

# Install debugging tools
RUN pip install debugpy

# Set development environment
ENV ENVIRONMENT=development \
    DEBUG=true

# Switch to non-root user
USER appuser

# Expose debug port
EXPOSE 8000 5678

# Development command with hot reload
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

# Testing stage
FROM development as testing

# Copy test files
COPY tests/ ./tests/
COPY pytest.ini ./
COPY .coveragerc ./

# Install test dependencies
RUN pip install pytest pytest-cov pytest-asyncio pytest-mock

# Set test environment
ENV ENVIRONMENT=testing \
    DATABASE_URL=sqlite:///./test.db

# Run tests
CMD ["python", "-m", "pytest", "tests/", "-v", "--cov=core_infra", "--cov-report=html", "--cov-report=term"]

# Security scanning stage
FROM production as security-scan

# Install security scanning tools
USER root
RUN pip install bandit safety

# Run security scans
RUN bandit -r core_infra/ -f json -o bandit-report.json || true
RUN safety check --json --output safety-report.json || true

# Switch back to app user
USER appuser

# Backup stage
FROM alpine:latest as backup

# Install backup tools
RUN apk add --no-cache \
    postgresql-client \
    aws-cli \
    curl \
    bash \
    dcron

# Create backup user
RUN addgroup -g 1000 backup && \
    adduser -D -s /bin/bash -u 1000 -G backup backup

# Copy backup scripts
COPY deployment/training-portal/scripts/backup.sh /usr/local/bin/
COPY deployment/training-portal/scripts/restore.sh /usr/local/bin/

# Make scripts executable
RUN chmod +x /usr/local/bin/backup.sh /usr/local/bin/restore.sh

# Create backup directory
RUN mkdir -p /backups && chown backup:backup /backups

# Switch to backup user
USER backup

# Set working directory
WORKDIR /backups

# Default command
CMD ["crond", "-f", "-d", "8"]

# Monitoring stage
FROM prom/prometheus:latest as monitoring

# Copy Prometheus configuration
COPY deployment/training-portal/monitoring/prometheus.yml /etc/prometheus/

# Copy custom rules
COPY deployment/training-portal/monitoring/rules/ /etc/prometheus/rules/

# Set ownership
USER root
RUN chown -R prometheus:prometheus /etc/prometheus/
USER prometheus

# Migration stage
FROM builder as migration

# Copy migration scripts
COPY alembic/ ./alembic/
COPY alembic.ini ./

# Set migration environment
ENV ENVIRONMENT=migration

# Switch to app user
USER appuser

# Migration command
CMD ["python", "-m", "alembic", "upgrade", "head"]

# Load testing stage
FROM loadimpact/k6:latest as load-test

# Copy load test scripts
COPY tests/performance/ /scripts/

# Set working directory
WORKDIR /scripts

# Default load test command
CMD ["run", "training-portal-load-test.js"]

# Documentation stage
FROM node:18-alpine as docs

# Install documentation tools
RUN npm install -g @apidevtools/swagger-parser redoc-cli

# Copy API documentation
COPY docs/api/ /docs/api/
COPY core_infra/api/training_portal/openapi.json /docs/

# Generate documentation
WORKDIR /docs
RUN redoc-cli build openapi.json --output index.html

# Serve documentation
EXPOSE 8080
CMD ["npx", "http-server", ".", "-p", "8080"]

# Final production image with labels
FROM production as final

# Add labels for better container management
LABEL maintainer="RegulensAI Team <<EMAIL>>" \
      version="${APP_VERSION}" \
      description="RegulensAI Training Portal API" \
      org.opencontainers.image.title="RegulensAI Training Portal API" \
      org.opencontainers.image.description="Enterprise training portal for compliance and regulatory training" \
      org.opencontainers.image.version="${APP_VERSION}" \
      org.opencontainers.image.vendor="RegulensAI" \
      org.opencontainers.image.licenses="Proprietary" \
      org.opencontainers.image.source="https://github.com/gaigenticai/RegulensAI" \
      org.opencontainers.image.documentation="https://docs.regulens.ai/training-portal"

# Add build info
ARG BUILD_DATE
ARG VCS_REF
LABEL org.opencontainers.image.created="${BUILD_DATE}" \
      org.opencontainers.image.revision="${VCS_REF}"

# Security labels
LABEL security.scan.enabled="true" \
      security.vulnerability.check="true" \
      security.compliance.level="enterprise"
