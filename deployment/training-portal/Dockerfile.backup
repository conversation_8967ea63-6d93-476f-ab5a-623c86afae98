# RegulensAI Database Backup Container
# Enterprise-grade backup solution with automated scheduling and S3 integration

FROM alpine:3.18

# Set build arguments
ARG BUILD_DATE
ARG VERSION=1.0.0

# Add metadata labels
LABEL maintainer="RegulensAI Team <<EMAIL>>" \
      version="${VERSION}" \
      description="RegulensAI Database Backup Service" \
      build-date="${BUILD_DATE}"

# Install required packages
RUN apk add --no-cache \
    postgresql15-client \
    aws-cli \
    curl \
    bash \
    dcron \
    tzdata \
    ca-certificates \
    gzip \
    tar \
    && rm -rf /var/cache/apk/*

# Create backup user and group
RUN addgroup -g 1000 backup && \
    adduser -D -s /bin/bash -u 1000 -G backup backup

# Create necessary directories
RUN mkdir -p /backups /scripts /var/log/backup && \
    chown -R backup:backup /backups /scripts /var/log/backup

# Copy backup scripts
COPY deployment/training-portal/scripts/backup.sh /scripts/
COPY deployment/training-portal/scripts/restore.sh /scripts/

# Make scripts executable
RUN chmod +x /scripts/*.sh

# Create cron configuration
RUN echo "# RegulensAI Backup Cron Jobs" > /etc/crontabs/backup && \
    echo "# Default: Daily backup at 2 AM" >> /etc/crontabs/backup && \
    echo "0 2 * * * /scripts/backup.sh >> /var/log/backup/cron.log 2>&1" >> /etc/crontabs/backup && \
    chown backup:backup /etc/crontabs/backup

# Create backup monitoring script
RUN cat > /scripts/backup-monitor.sh << 'EOF'
#!/bin/bash
# Backup monitoring and health check script

HEALTH_CHECK_FILE="/backups/.health"
MAX_AGE_HOURS=25  # Allow 1 hour grace period for daily backups

# Check if backup is recent
if [[ -f "$HEALTH_CHECK_FILE" ]]; then
    last_backup=$(cat "$HEALTH_CHECK_FILE")
    current_time=$(date +%s)
    age_hours=$(( (current_time - last_backup) / 3600 ))
    
    if [[ $age_hours -lt $MAX_AGE_HOURS ]]; then
        echo "OK: Last backup was $age_hours hours ago"
        exit 0
    else
        echo "CRITICAL: Last backup was $age_hours hours ago"
        exit 1
    fi
else
    echo "CRITICAL: No backup health check file found"
    exit 1
fi
EOF

RUN chmod +x /scripts/backup-monitor.sh

# Create backup wrapper script that updates health check
RUN cat > /scripts/backup-wrapper.sh << 'EOF'
#!/bin/bash
# Wrapper script for backup with health check updates

set -euo pipefail

HEALTH_CHECK_FILE="/backups/.health"
LOG_FILE="/var/log/backup/backup.log"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Log start
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Starting scheduled backup" >> "$LOG_FILE"

# Run backup
if /scripts/backup.sh >> "$LOG_FILE" 2>&1; then
    # Update health check on success
    date +%s > "$HEALTH_CHECK_FILE"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup completed successfully" >> "$LOG_FILE"
    exit 0
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Backup failed" >> "$LOG_FILE"
    exit 1
fi
EOF

RUN chmod +x /scripts/backup-wrapper.sh

# Update cron to use wrapper script
RUN sed -i 's|/scripts/backup.sh|/scripts/backup-wrapper.sh|' /etc/crontabs/backup

# Create log rotation configuration
RUN cat > /etc/logrotate.d/backup << 'EOF'
/var/log/backup/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
    create 0644 backup backup
}
EOF

# Create entrypoint script
RUN cat > /scripts/entrypoint.sh << 'EOF'
#!/bin/bash
# Backup service entrypoint

set -euo pipefail

# Set timezone if provided
if [[ -n "${TZ:-}" ]]; then
    ln -snf "/usr/share/zoneinfo/$TZ" /etc/localtime
    echo "$TZ" > /etc/timezone
fi

# Update cron schedule if provided
if [[ -n "${BACKUP_SCHEDULE:-}" ]]; then
    echo "Updating backup schedule to: $BACKUP_SCHEDULE"
    echo "$BACKUP_SCHEDULE /scripts/backup-wrapper.sh >> /var/log/backup/cron.log 2>&1" > /etc/crontabs/backup
fi

# Validate environment variables
required_vars=("DATABASE_URL" "S3_BUCKET")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        echo "ERROR: Required environment variable $var is not set"
        exit 1
    fi
done

# Test database connectivity
echo "Testing database connectivity..."
if ! pg_isready -d "$DATABASE_URL" -q; then
    echo "WARNING: Database is not accessible. Backup service will continue but backups may fail."
fi

# Test S3 connectivity
echo "Testing S3 connectivity..."
if ! aws s3 ls "s3://$S3_BUCKET" &> /dev/null; then
    echo "WARNING: S3 bucket is not accessible. Backup service will continue but uploads may fail."
fi

# Create initial health check
date +%s > /backups/.health

# Start cron daemon
echo "Starting backup service..."
echo "Database: $DATABASE_URL"
echo "S3 Bucket: $S3_BUCKET"
echo "Schedule: ${BACKUP_SCHEDULE:-0 2 * * *}"
echo "Retention: ${RETENTION_DAYS:-30} days"

# Start cron in foreground
exec crond -f -l 2
EOF

RUN chmod +x /scripts/entrypoint.sh

# Create health check script for Docker
RUN cat > /scripts/healthcheck.sh << 'EOF'
#!/bin/bash
# Docker health check script

# Check if cron is running
if ! pgrep crond > /dev/null; then
    echo "CRITICAL: Cron daemon is not running"
    exit 1
fi

# Check backup health
/scripts/backup-monitor.sh
EOF

RUN chmod +x /scripts/healthcheck.sh

# Switch to backup user
USER backup

# Set working directory
WORKDIR /backups

# Set environment variables
ENV BACKUP_SCHEDULE="0 2 * * *" \
    RETENTION_DAYS="30" \
    COMPRESSION_LEVEL="6" \
    BACKUP_PREFIX="training-portal"

# Health check
HEALTHCHECK --interval=1h --timeout=30s --start-period=1m --retries=3 \
    CMD /scripts/healthcheck.sh

# Expose volume for backups
VOLUME ["/backups", "/var/log/backup"]

# Set entrypoint
ENTRYPOINT ["/scripts/entrypoint.sh"]

# Default command (can be overridden)
CMD []
