apiVersion: v1
kind: Namespace
metadata:
  name: regulens-training
  labels:
    name: regulens-training
    environment: production

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: training-portal-config
  namespace: regulens-training
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  METRICS_ENABLED: "true"
  CACHE_TTL: "3600"
  MAX_WORKERS: "4"
  DATABASE_HOST: "training-postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "regulens_training"
  REDIS_HOST: "training-redis-service"
  REDIS_PORT: "6379"

---
apiVersion: v1
kind: Secret
metadata:
  name: training-portal-secrets
  namespace: regulens-training
type: Opaque
data:
  DATABASE_PASSWORD: <base64-encoded-password>
  SECRET_KEY: <base64-encoded-secret>
  JWT_SECRET_KEY: <base64-encoded-jwt-secret>
  ENCRYPTION_KEY: <base64-encoded-encryption-key>
  REDIS_PASSWORD: <base64-encoded-redis-password>

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-api-deployment
  namespace: regulens-training
  labels:
    app: training-api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: training-api
  template:
    metadata:
      labels:
        app: training-api
        version: v1
    spec:
      containers:
      - name: training-api
        image: regulens/training-api:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          value: "postgresql://regulens:$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@$(REDIS_HOST):$(REDIS_PORT)/0"
        envFrom:
        - configMapRef:
            name: training-portal-config
        - secretRef:
            name: training-portal-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/training/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/training/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: training-logs
          mountPath: /app/logs
        - name: training-uploads
          mountPath: /app/uploads
        - name: training-certificates
          mountPath: /app/certificates
      volumes:
      - name: training-logs
        persistentVolumeClaim:
          claimName: training-logs-pvc
      - name: training-uploads
        persistentVolumeClaim:
          claimName: training-uploads-pvc
      - name: training-certificates
        persistentVolumeClaim:
          claimName: training-certificates-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: training-api-service
  namespace: regulens-training
  labels:
    app: training-api
spec:
  selector:
    app: training-api
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-frontend-deployment
  namespace: regulens-training
  labels:
    app: training-frontend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: training-frontend
  template:
    metadata:
      labels:
        app: training-frontend
        version: v1
    spec:
      containers:
      - name: training-frontend
        image: regulens/training-frontend:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: REACT_APP_API_URL
          value: "http://training-api-service:8000/api/v1"
        - name: REACT_APP_ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: training-frontend-service
  namespace: regulens-training
  labels:
    app: training-frontend
spec:
  selector:
    app: training-frontend
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: training-postgres
  namespace: regulens-training
  labels:
    app: training-postgres
spec:
  serviceName: training-postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: training-postgres
  template:
    metadata:
      labels:
        app: training-postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: training-portal-config
              key: DATABASE_NAME
        - name: POSTGRES_USER
          value: "regulens"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: DATABASE_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - regulens
            - -d
            - regulens_training
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - regulens
            - -d
            - regulens_training
          initialDelaySeconds: 5
          periodSeconds: 5
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: training-postgres-service
  namespace: regulens-training
  labels:
    app: training-postgres
spec:
  selector:
    app: training-postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-redis-deployment
  namespace: regulens-training
  labels:
    app: training-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: training-redis
  template:
    metadata:
      labels:
        app: training-redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --appendonly
        - "yes"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: training-redis-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: training-redis-service
  namespace: regulens-training
  labels:
    app: training-redis
spec:
  selector:
    app: training-redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: training-portal-ingress
  namespace: regulens-training
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  tls:
  - hosts:
    - training.regulens.ai
    secretName: training-portal-tls
  rules:
  - host: training.regulens.ai
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: training-api-service
            port:
              number: 8000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: training-frontend-service
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: training-api-hpa
  namespace: regulens-training
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: training-api-deployment
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: training-frontend-hpa
  namespace: regulens-training
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: training-frontend-deployment
  minReplicas: 2
  maxReplicas: 6
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
