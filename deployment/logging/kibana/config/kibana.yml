# RegulensAI Kibana Configuration
# Data visualization and management interface

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================
server.name: "regulensai-kibana"
server.host: "0.0.0.0"
server.port: 5601
server.basePath: ""
server.rewriteBasePath: false

# ============================================================================
# ELASTICSEARCH CONFIGURATION
# ============================================================================
elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: "elastic"
elasticsearch.password: "${ELASTICSEARCH_PASSWORD}"
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000
elasticsearch.pingTimeout: 1500

# ============================================================================
# KIBANA INDEX CONFIGURATION
# ============================================================================
kibana.index: ".kibana"
kibana.defaultAppId: "dashboard"

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
logging.appenders:
  file:
    type: file
    fileName: /usr/share/kibana/logs/kibana.log
    layout:
      type: json
  console:
    type: console
    layout:
      type: pattern
      pattern: "[%date][%level][%logger] %message"

logging.root:
  appenders: [file, console]
  level: info

logging.loggers:
  - name: elasticsearch
    level: warn
  - name: elasticsearch.query
    level: warn

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================
monitoring.enabled: true
monitoring.kibana.collection.enabled: true
monitoring.kibana.collection.interval: 10000

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
xpack.security.enabled: true
xpack.security.encryptionKey: "${XPACK_SECURITY_ENCRYPTIONKEY}"
xpack.encryptedSavedObjects.encryptionKey: "${XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY}"
xpack.reporting.encryptionKey: "${XPACK_REPORTING_ENCRYPTIONKEY}"

# Session configuration
xpack.security.session.idleTimeout: "1h"
xpack.security.session.lifespan: "8h"

# ============================================================================
# REPORTING CONFIGURATION
# ============================================================================
xpack.reporting.enabled: true
xpack.reporting.kibanaServer.hostname: "localhost"
xpack.reporting.kibanaServer.port: 5601
xpack.reporting.capture.browser.chromium.disableSandbox: true

# ============================================================================
# ALERTING CONFIGURATION
# ============================================================================
xpack.alerting.enabled: true
xpack.actions.enabled: true

# ============================================================================
# MACHINE LEARNING CONFIGURATION
# ============================================================================
xpack.ml.enabled: true

# ============================================================================
# CANVAS CONFIGURATION
# ============================================================================
xpack.canvas.enabled: true

# ============================================================================
# MAPS CONFIGURATION
# ============================================================================
xpack.maps.enabled: true

# ============================================================================
# GRAPH CONFIGURATION
# ============================================================================
xpack.graph.enabled: true

# ============================================================================
# CUSTOM CONFIGURATION FOR REGULENSAI
# ============================================================================

# Default index patterns
kibana.defaultIndex: "regulensai-application-*"

# Custom branding
server.name: "RegulensAI Log Analytics"

# Performance settings
elasticsearch.requestHeadersWhitelist: ["authorization", "x-forwarded-for", "x-forwarded-proto", "x-proxy-user", "x-proxy-roles"]

# Data views configuration
data.search.aggs.shardDelay.enabled: true

# ============================================================================
# TELEMETRY CONFIGURATION
# ============================================================================
telemetry.enabled: false
telemetry.optIn: false

# ============================================================================
# NEWSFEED CONFIGURATION
# ============================================================================
newsfeed.enabled: false

# ============================================================================
# SAVED OBJECTS CONFIGURATION
# ============================================================================
savedObjects.maxImportPayloadBytes: 26214400

# ============================================================================
# VISUALIZATION CONFIGURATION
# ============================================================================
vis_type_vega.enableExternalUrls: false

# ============================================================================
# CONSOLE CONFIGURATION
# ============================================================================
console.enabled: true

# ============================================================================
# TIMELION CONFIGURATION
# ============================================================================
timelion.enabled: true

# ============================================================================
# DASHBOARD CONFIGURATION
# ============================================================================
dashboard.defaultDarkMode: false

# ============================================================================
# DISCOVER CONFIGURATION
# ============================================================================
discover.sampleSize: 500

# ============================================================================
# CUSTOM KIBANA PLUGINS CONFIGURATION
# ============================================================================
# Add any custom plugin configurations here

# ============================================================================
# ADVANCED SETTINGS
# ============================================================================
# Maximum number of buckets for aggregations
data.search.aggs.buckets.maxBuckets: 65535

# Search timeout
elasticsearch.requestTimeout: 30000

# Index pattern cache
data.indexPatterns.cache.enabled: true

# ============================================================================
# DEVELOPMENT SETTINGS (for development environment only)
# ============================================================================
# Uncomment for development
# server.rewriteBasePath: true
# logging.verbose: true
# elasticsearch.logQueries: true
