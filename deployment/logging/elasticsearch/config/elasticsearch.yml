# RegulensAI Elasticsearch Configuration
# Enterprise-grade search and analytics engine configuration

# ============================================================================
# CLUSTER CONFIGURATION
# ============================================================================
cluster.name: regulensai-logs
node.name: elasticsearch-node-1
node.roles: [ master, data, ingest ]

# ============================================================================
# NETWORK CONFIGURATION
# ============================================================================
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# ============================================================================
# DISCOVERY CONFIGURATION
# ============================================================================
discovery.type: single-node
cluster.initial_master_nodes: ["elasticsearch-node-1"]

# ============================================================================
# MEMORY CONFIGURATION
# ============================================================================
bootstrap.memory_lock: true
indices.memory.index_buffer_size: 30%
indices.memory.min_index_buffer_size: 96mb

# ============================================================================
# STORAGE CONFIGURATION
# ============================================================================
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================
xpack.security.enabled: true
xpack.security.authc.api_key.enabled: true
xpack.security.transport.ssl.enabled: false
xpack.security.http.ssl.enabled: false

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================
xpack.monitoring.collection.enabled: true
xpack.monitoring.elasticsearch.collection.enabled: true

# ============================================================================
# INDEX LIFECYCLE MANAGEMENT
# ============================================================================
xpack.ilm.enabled: true

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================
# Thread pools
thread_pool.write.queue_size: 1000
thread_pool.search.queue_size: 1000
thread_pool.get.queue_size: 1000

# Circuit breakers
indices.breaker.total.limit: 70%
indices.breaker.fielddata.limit: 40%
indices.breaker.request.limit: 40%

# Query cache
indices.queries.cache.size: 20%

# ============================================================================
# INDEX TEMPLATES FOR REGULENSAI LOGS
# ============================================================================
# These will be applied automatically to matching indices

# Application logs template
index.template.application:
  index_patterns: ["regulensai-application-*"]
  settings:
    number_of_shards: 1
    number_of_replicas: 1
    refresh_interval: 30s
    index.lifecycle.name: regulensai-logs-policy
    index.lifecycle.rollover_alias: regulensai-application-logs
  mappings:
    properties:
      "@timestamp":
        type: date
      timestamp:
        type: date
      level:
        type: keyword
      category:
        type: keyword
      service:
        type: keyword
      message:
        type: text
        analyzer: standard
      logger_name:
        type: keyword
      module:
        type: keyword
      function:
        type: keyword
      line_number:
        type: integer
      thread_id:
        type: keyword
      process_id:
        type: integer
      user_id:
        type: keyword
      tenant_id:
        type: keyword
      request_id:
        type: keyword
      session_id:
        type: keyword
      correlation_id:
        type: keyword
      tags:
        type: object
        dynamic: true
      extra_data:
        type: object
        dynamic: true
      stack_trace:
        type: text
        index: false
      duration_ms:
        type: float

# Security logs template
index.template.security:
  index_patterns: ["regulensai-security-*"]
  settings:
    number_of_shards: 1
    number_of_replicas: 2  # Extra replicas for security logs
    refresh_interval: 10s  # Faster refresh for security events
    index.lifecycle.name: regulensai-security-logs-policy
    index.lifecycle.rollover_alias: regulensai-security-logs
  mappings:
    properties:
      "@timestamp":
        type: date
      timestamp:
        type: date
      level:
        type: keyword
      category:
        type: keyword
      service:
        type: keyword
      message:
        type: text
        analyzer: standard
      user_id:
        type: keyword
      source_ip:
        type: ip
      user_agent:
        type: text
        analyzer: standard
      action:
        type: keyword
      resource:
        type: keyword
      result:
        type: keyword
      risk_score:
        type: float

# Performance logs template
index.template.performance:
  index_patterns: ["regulensai-performance-*"]
  settings:
    number_of_shards: 1
    number_of_replicas: 1
    refresh_interval: 60s
    index.lifecycle.name: regulensai-performance-logs-policy
    index.lifecycle.rollover_alias: regulensai-performance-logs
  mappings:
    properties:
      "@timestamp":
        type: date
      timestamp:
        type: date
      level:
        type: keyword
      category:
        type: keyword
      service:
        type: keyword
      message:
        type: text
        analyzer: standard
      operation:
        type: keyword
      duration_ms:
        type: float
      cpu_usage:
        type: float
      memory_usage:
        type: float
      response_time:
        type: float
      throughput:
        type: float
      error_rate:
        type: float

# Audit logs template
index.template.audit:
  index_patterns: ["regulensai-audit-*"]
  settings:
    number_of_shards: 1
    number_of_replicas: 2  # Extra replicas for audit logs
    refresh_interval: 5s   # Very fast refresh for audit events
    index.lifecycle.name: regulensai-audit-logs-policy
    index.lifecycle.rollover_alias: regulensai-audit-logs
  mappings:
    properties:
      "@timestamp":
        type: date
      timestamp:
        type: date
      level:
        type: keyword
      category:
        type: keyword
      service:
        type: keyword
      message:
        type: text
        analyzer: standard
      user_id:
        type: keyword
      tenant_id:
        type: keyword
      action:
        type: keyword
      resource:
        type: keyword
      resource_id:
        type: keyword
      old_value:
        type: text
        index: false
      new_value:
        type: text
        index: false
      source_ip:
        type: ip
      user_agent:
        type: text
        analyzer: standard

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
logger.level: INFO
logger.org.elasticsearch.discovery: WARN
logger.org.elasticsearch.cluster.service: WARN

# ============================================================================
# SNAPSHOT CONFIGURATION
# ============================================================================
path.repo: ["/usr/share/elasticsearch/backups"]

# ============================================================================
# CUSTOM SETTINGS FOR REGULENSAI
# ============================================================================
# Maximum number of clauses in a query
indices.query.bool.max_clause_count: 10000

# Maximum script compilation rate
script.max_compilations_rate: 150/5m

# Action timeout
action.destructive_requires_name: true

# ============================================================================
# DEPRECATION LOGGING
# ============================================================================
logger.deprecation.level: WARN
