# RegulensAI Logstash Pipeline Configuration
# Intelligent log processing, parsing, and enrichment

# ============================================================================
# INPUT PLUGINS
# ============================================================================

input {
  # Beats input for Filebeat
  beats {
    port => 5044
    type => "beats"
  }
  
  # TCP input for direct log shipping
  tcp {
    port => 5000
    type => "tcp"
    codec => json_lines
  }
  
  # HTTP input for webhook logs
  http {
    port => 8080
    type => "http"
    codec => json
  }
  
  # Syslog input
  syslog {
    port => 5514
    type => "syslog"
  }
}

# ============================================================================
# FILTER PLUGINS
# ============================================================================

filter {
  # Add processing timestamp
  mutate {
    add_field => { "[@metadata][processed_at]" => "%{+YYYY-MM-dd'T'HH:mm:ss.SSSZ}" }
  }
  
  # Parse RegulensAI structured logs
  if [type] == "beats" or [type] == "tcp" {
    # Parse JSON logs
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "parsed"
      }
      
      # Move parsed fields to root level
      if [parsed] {
        ruby {
          code => "
            parsed = event.get('parsed')
            if parsed.is_a?(Hash)
              parsed.each { |k, v| event.set(k, v) }
              event.remove('parsed')
            end
          "
        }
      }
    }
    
    # Parse timestamp if present
    if [@timestamp] {
      date {
        match => [ "@timestamp", "ISO8601" ]
        target => "@timestamp"
      }
    } else if [timestamp] {
      date {
        match => [ "timestamp", "ISO8601" ]
        target => "@timestamp"
      }
    }
    
    # Normalize log level
    if [level] {
      mutate {
        uppercase => [ "level" ]
      }
    }
    
    # Extract user information from context
    if [user_id] {
      mutate {
        add_field => { "user.id" => "%{user_id}" }
      }
    }
    
    if [tenant_id] {
      mutate {
        add_field => { "tenant.id" => "%{tenant_id}" }
      }
    }
    
    # Extract request information
    if [request_id] {
      mutate {
        add_field => { "request.id" => "%{request_id}" }
      }
    }
    
    if [session_id] {
      mutate {
        add_field => { "session.id" => "%{session_id}" }
      }
    }
    
    # Extract performance metrics
    if [duration_ms] {
      mutate {
        convert => { "duration_ms" => "float" }
        add_field => { "performance.duration_ms" => "%{duration_ms}" }
      }
    }
    
    # Categorize logs based on content
    if [category] {
      mutate {
        add_field => { "log.category" => "%{category}" }
      }
    } else {
      # Auto-categorize based on content
      if [message] =~ /(?i)(login|logout|authentication|authorization|access denied|permission)/ {
        mutate {
          add_field => { "log.category" => "security" }
        }
      } else if [message] =~ /(?i)(error|exception|failed|failure|critical)/ {
        mutate {
          add_field => { "log.category" => "error" }
        }
      } else if [message] =~ /(?i)(performance|slow|timeout|latency|response time)/ {
        mutate {
          add_field => { "log.category" => "performance" }
        }
      } else if [message] =~ /(?i)(audit|compliance|regulation|policy)/ {
        mutate {
          add_field => { "log.category" => "audit" }
        }
      } else if [message] =~ /(?i)(backup|restore|migration|database)/ {
        mutate {
          add_field => { "log.category" => "system" }
        }
      } else {
        mutate {
          add_field => { "log.category" => "application" }
        }
      }
    }
    
    # Extract IP addresses
    if [message] {
      grok {
        match => { "message" => "%{IP:extracted_ip}" }
        tag_on_failure => []
      }
      
      if [extracted_ip] {
        mutate {
          add_field => { "network.client_ip" => "%{extracted_ip}" }
        }
      }
    }
    
    # GeoIP enrichment for IP addresses
    if [network.client_ip] {
      geoip {
        source => "network.client_ip"
        target => "geoip"
      }
    }
    
    # Extract error information
    if [level] == "ERROR" or [level] == "CRITICAL" {
      if [stack_trace] {
        mutate {
          add_field => { "error.stack_trace" => "%{stack_trace}" }
        }
      }
      
      # Extract error patterns
      grok {
        match => { "message" => "(?<error.type>[A-Za-z][A-Za-z0-9]*Exception)" }
        tag_on_failure => []
      }
    }
    
    # Security event enrichment
    if [log.category] == "security" {
      # Extract authentication events
      if [message] =~ /(?i)login/ {
        mutate {
          add_field => { "security.event_type" => "authentication" }
        }
        
        if [message] =~ /(?i)failed|denied/ {
          mutate {
            add_field => { "security.outcome" => "failure" }
            add_field => { "security.risk_score" => "7" }
          }
        } else if [message] =~ /(?i)success|successful/ {
          mutate {
            add_field => { "security.outcome" => "success" }
            add_field => { "security.risk_score" => "2" }
          }
        }
      }
      
      # Extract authorization events
      if [message] =~ /(?i)(access denied|permission|unauthorized)/ {
        mutate {
          add_field => { "security.event_type" => "authorization" }
          add_field => { "security.outcome" => "failure" }
          add_field => { "security.risk_score" => "8" }
        }
      }
    }
    
    # Performance event enrichment
    if [log.category] == "performance" {
      if [duration_ms] {
        if [duration_ms] > 5000 {
          mutate {
            add_field => { "performance.severity" => "high" }
          }
        } else if [duration_ms] > 1000 {
          mutate {
            add_field => { "performance.severity" => "medium" }
          }
        } else {
          mutate {
            add_field => { "performance.severity" => "low" }
          }
        }
      }
    }
    
    # Compliance event enrichment
    if [log.category] == "audit" or [log.category] == "compliance" {
      mutate {
        add_field => { "compliance.retention_required" => "true" }
        add_field => { "compliance.category" => "financial_regulation" }
      }
      
      # Extract regulation references
      if [message] =~ /(?i)(basel|mifid|gdpr|sox|ccar|dodd-frank)/ {
        grok {
          match => { "message" => "(?i)(?<compliance.regulation>basel|mifid|gdpr|sox|ccar|dodd-frank)" }
          tag_on_failure => []
        }
      }
    }
    
    # Clean up temporary fields
    mutate {
      remove_field => [ "extracted_ip", "host", "agent" ]
    }
  }
  
  # Handle syslog messages
  if [type] == "syslog" {
    mutate {
      add_field => { "log.category" => "system" }
    }
  }
  
  # Handle HTTP webhook logs
  if [type] == "http" {
    mutate {
      add_field => { "log.category" => "webhook" }
    }
  }
  
  # Add common fields
  mutate {
    add_field => { 
      "service.name" => "regulensai"
      "service.version" => "${SERVICE_VERSION:unknown}"
      "environment" => "${ENVIRONMENT:production}"
    }
  }
  
  # Final cleanup and validation
  if ![level] {
    mutate {
      add_field => { "level" => "INFO" }
    }
  }
  
  if ![log.category] {
    mutate {
      add_field => { "log.category" => "application" }
    }
  }
}

# ============================================================================
# OUTPUT PLUGINS
# ============================================================================

output {
  # Output to Elasticsearch with dynamic index routing
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    user => "elastic"
    password => "${ELASTIC_PASSWORD}"
    
    # Dynamic index based on category and date
    index => "regulensai-%{[log.category]}-%{+YYYY.MM.dd}"
    
    # Document type
    document_type => "_doc"
    
    # Template management
    manage_template => true
    template_name => "regulensai-logs"
    template_pattern => "regulensai-*"
    template_overwrite => true
    
    # Performance settings
    flush_size => 1000
    idle_flush_time => 10
    
    # Retry settings
    retry_max_interval => 30
    retry_max_times => 5
  }
  
  # Output to file for debugging (optional)
  if [@metadata][debug] == "true" {
    file {
      path => "/usr/share/logstash/logs/debug-%{+YYYY-MM-dd}.log"
      codec => json_lines
    }
  }
  
  # Output critical errors to separate index
  if [level] == "CRITICAL" or [security.risk_score] and [security.risk_score] >= 8 {
    elasticsearch {
      hosts => ["http://elasticsearch:9200"]
      user => "elastic"
      password => "${ELASTIC_PASSWORD}"
      index => "regulensai-critical-%{+YYYY.MM.dd}"
      document_type => "_doc"
    }
  }
  
  # Output to stdout for debugging (development only)
  if "${ENVIRONMENT}" == "development" {
    stdout {
      codec => rubydebug
    }
  }
}
