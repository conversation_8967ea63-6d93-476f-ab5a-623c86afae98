# RegulensAI Logstash Configuration
# Data processing pipeline for log transformation and enrichment

# ============================================================================
# NODE CONFIGURATION
# ============================================================================
node.name: regulensai-logstash
path.data: /usr/share/logstash/data
path.logs: /usr/share/logstash/logs
path.settings: /usr/share/logstash/config

# ============================================================================
# PIPELINE CONFIGURATION
# ============================================================================
pipeline.id: regulensai-main
pipeline.workers: 4
pipeline.batch.size: 1000
pipeline.batch.delay: 50

# ============================================================================
# QUEUE CONFIGURATION
# ============================================================================
queue.type: persisted
queue.max_events: 10000
queue.max_bytes: 1gb
queue.checkpoint.writes: 1024

# ============================================================================
# MONITORING CONFIGURATION
# ============================================================================
monitoring.enabled: true
monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
monitoring.elasticsearch.username: elastic
monitoring.elasticsearch.password: "${ELASTIC_PASSWORD}"

# ============================================================================
# HTTP API CONFIGURATION
# ============================================================================
http.host: "0.0.0.0"
http.port: 9600

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
log.level: info
log.format: json

# ============================================================================
# PERFORMANCE TUNING
# ============================================================================
# JVM heap size (set via LS_JAVA_OPTS environment variable)
# -Xms1g -Xmx1g

# Pipeline settings for high throughput
pipeline.unsafe_shutdown: false
pipeline.plugin_classloaders: false

# ============================================================================
# DEAD LETTER QUEUE
# ============================================================================
dead_letter_queue.enable: true
dead_letter_queue.max_bytes: 1gb

# ============================================================================
# XPACK CONFIGURATION
# ============================================================================
xpack.monitoring.enabled: true
xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
xpack.monitoring.elasticsearch.username: elastic
xpack.monitoring.elasticsearch.password: "${ELASTIC_PASSWORD}"

# ============================================================================
# CONFIG RELOAD
# ============================================================================
config.reload.automatic: true
config.reload.interval: 3s

# ============================================================================
# MODULES
# ============================================================================
modules:
  - name: netflow
    var.elasticsearch.hosts: "http://elasticsearch:9200"
    var.elasticsearch.username: "elastic"
    var.elasticsearch.password: "${ELASTIC_PASSWORD}"
  
  - name: azure
    var.elasticsearch.hosts: "http://elasticsearch:9200"
    var.elasticsearch.username: "elastic"
    var.elasticsearch.password: "${ELASTIC_PASSWORD}"
