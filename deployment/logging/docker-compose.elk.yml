version: '3.8'

# <PERSON>ulensA<PERSON> ELK Stack for Centralized Logging
# Enterprise-grade log aggregation with Elasticsearch, Logstash, Kibana, and Filebeat

services:
  # ============================================================================
  # ELASTICSEARCH - Search and Analytics Engine
  # ============================================================================
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: regulensai-elasticsearch
    hostname: elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=regulensai-logs
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - xpack.security.enabled=true
      - xpack.security.authc.api_key.enabled=true
      - xpack.security.transport.ssl.enabled=false
      - xpack.security.http.ssl.enabled=false
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-regulensai123}
      - xpack.license.self_generated.type=basic
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    mem_limit: 4g
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml:ro
      - ./elasticsearch/config/log4j2.properties:/usr/share/elasticsearch/config/log4j2.properties:ro
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - elk
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    labels:
      - "com.regulensai.service=elasticsearch"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

  # ============================================================================
  # LOGSTASH - Data Processing Pipeline
  # ============================================================================
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: regulensai-logstash
    hostname: logstash
    environment:
      - "LS_JAVA_OPTS=-Xmx1g -Xms1g"
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-regulensai123}
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - ./logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ./logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./logstash/patterns:/usr/share/logstash/patterns:ro
    ports:
      - "5044:5044"  # Beats input
      - "5000:5000"  # TCP input
      - "9600:9600"  # Logstash monitoring
    networks:
      - elk
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9600/_node/stats || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    labels:
      - "com.regulensai.service=logstash"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

  # ============================================================================
  # KIBANA - Data Visualization and Management
  # ============================================================================
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: regulensai-kibana
    hostname: kibana
    environment:
      - SERVERNAME=kibana
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTICSEARCH_PASSWORD=${ELASTIC_PASSWORD:-regulensai123}
      - XPACK_SECURITY_ENABLED=true
      - XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY=${KIBANA_ENCRYPTION_KEY:-regulensai-kibana-encryption-key-32-chars}
      - XPACK_REPORTING_ENCRYPTIONKEY=${KIBANA_REPORTING_KEY:-regulensai-kibana-reporting-key-32-chars}
      - XPACK_SECURITY_ENCRYPTIONKEY=${KIBANA_SECURITY_KEY:-regulensai-kibana-security-key-32-chars}
    volumes:
      - ./kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
      - kibana_data:/usr/share/kibana/data
    ports:
      - "5601:5601"
    networks:
      - elk
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    restart: unless-stopped
    labels:
      - "com.regulensai.service=kibana"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

  # ============================================================================
  # FILEBEAT - Log Shipper
  # ============================================================================
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: regulensai-filebeat
    hostname: filebeat
    user: root
    environment:
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-regulensai123}
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - LOGSTASH_HOSTS=logstash:5044
    volumes:
      - ./filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/log:/var/log:ro
      - filebeat_data:/usr/share/filebeat/data
    command: filebeat -e -strict.perms=false
    networks:
      - elk
    depends_on:
      logstash:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "com.regulensai.service=filebeat"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

  # ============================================================================
  # METRICBEAT - System and Service Metrics
  # ============================================================================
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.11.0
    container_name: regulensai-metricbeat
    hostname: metricbeat
    user: root
    environment:
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-regulensai123}
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - ./metricbeat/config/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat_data:/usr/share/metricbeat/data
    command: metricbeat -e -strict.perms=false -system.hostfs=/hostfs
    networks:
      - elk
    depends_on:
      elasticsearch:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "com.regulensai.service=metricbeat"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

  # ============================================================================
  # ELASTICSEARCH HEAD - Web Interface for Elasticsearch
  # ============================================================================
  elasticsearch-head:
    image: mobz/elasticsearch-head:5
    container_name: regulensai-elasticsearch-head
    hostname: elasticsearch-head
    ports:
      - "9100:9100"
    networks:
      - elk
    depends_on:
      - elasticsearch
    restart: unless-stopped
    labels:
      - "com.regulensai.service=elasticsearch-head"
      - "com.regulensai.environment=${ENVIRONMENT:-production}"

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  elk:
    driver: bridge
    name: regulensai-elk
    labels:
      - "com.regulensai.network=elk"

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  elasticsearch_data:
    driver: local
    name: regulensai-elasticsearch-data
    labels:
      - "com.regulensai.volume=elasticsearch-data"
  
  kibana_data:
    driver: local
    name: regulensai-kibana-data
    labels:
      - "com.regulensai.volume=kibana-data"
  
  filebeat_data:
    driver: local
    name: regulensai-filebeat-data
    labels:
      - "com.regulensai.volume=filebeat-data"
  
  metricbeat_data:
    driver: local
    name: regulensai-metricbeat-data
    labels:
      - "com.regulensai.volume=metricbeat-data"
