# Regulens AI - Blue-Green Deployment Template
# Template for blue-green deployments with environment variable substitution

apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulens-ai-${SLOT}
  namespace: regulens-ai
  labels:
    app: regulens-ai
    slot: ${SLOT}
    version: ${IMAGE_TAG}
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: regulens-ai
      slot: ${SLOT}
  template:
    metadata:
      labels:
        app: regulens-ai
        slot: ${SLOT}
        version: ${IMAGE_TAG}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: regulens-ai
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: regulens-ai
        image: ${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: APP_ENVIRONMENT
          value: "production"
        - name: DEBUG
          value: "false"
        - name: API_HOST
          value: "0.0.0.0"
        - name: API_PORT
          value: "8000"
        - name: DEPLOYMENT_SLOT
          value: ${SLOT}
        envFrom:
        - secretRef:
            name: regulens-ai-secrets
        - configMapRef:
            name: regulens-ai-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
        volumeMounts:
        - name: app-config
          mountPath: /app/config
          readOnly: true
        - name: temp-storage
          mountPath: /tmp
      volumes:
      - name: app-config
        configMap:
          name: regulens-ai-config
      - name: temp-storage
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - regulens-ai
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
# Service for the specific slot
apiVersion: v1
kind: Service
metadata:
  name: regulens-ai-${SLOT}
  namespace: regulens-ai
  labels:
    app: regulens-ai
    slot: ${SLOT}
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: regulens-ai
    slot: ${SLOT}

---
# Active service (traffic routing)
apiVersion: v1
kind: Service
metadata:
  name: regulens-ai-active
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: active-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  - port: 443
    targetPort: 8000
    protocol: TCP
    name: https
  selector:
    app: regulens-ai
    slot: ${SLOT}  # This will be updated during deployment

---
# ConfigMap for deployment configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: regulens-ai-config
  namespace: regulens-ai
  labels:
    app: regulens-ai
data:
  # Application configuration
  app.yaml: |
    server:
      host: "0.0.0.0"
      port: 8000
      workers: 4
    
    database:
      pool_size: 20
      max_overflow: 30
      pool_timeout: 30
      pool_recycle: 3600
    
    cache:
      redis_url: "redis://regulens-redis:6379/0"
      default_ttl: 300
      max_connections: 50
    
    monitoring:
      metrics_enabled: true
      health_check_interval: 30
      log_level: "INFO"
    
    security:
      cors_origins: ["https://app.regulens.ai", "https://api.regulens.ai"]
      rate_limit_per_minute: 1000
      jwt_expiry_hours: 24
  
  # Deployment metadata
  deployment.yaml: |
    slot: ${SLOT}
    image_tag: ${IMAGE_TAG}
    deployed_at: $(date -u +"%Y-%m-%dT%H:%M:%SZ")
    deployment_id: $(uuidgen)

---
# ServiceAccount for the application
apiVersion: v1
kind: ServiceAccount
metadata:
  name: regulens-ai
  namespace: regulens-ai
  labels:
    app: regulens-ai

---
# Role for the application
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: regulens-ai
  name: regulens-ai-role
  labels:
    app: regulens-ai
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]

---
# RoleBinding for the application
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: regulens-ai-rolebinding
  namespace: regulens-ai
  labels:
    app: regulens-ai
subjects:
- kind: ServiceAccount
  name: regulens-ai
  namespace: regulens-ai
roleRef:
  kind: Role
  name: regulens-ai-role
  apiGroup: rbac.authorization.k8s.io

---
# NetworkPolicy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: regulens-ai-${SLOT}-netpol
  namespace: regulens-ai
  labels:
    app: regulens-ai
    slot: ${SLOT}
spec:
  podSelector:
    matchLabels:
      app: regulens-ai
      slot: ${SLOT}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - podSelector:
        matchLabels:
          app: regulens-ai
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: regulens-redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS
