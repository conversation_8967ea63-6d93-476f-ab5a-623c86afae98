# Regulens AI - Load Balancer Configuration
# Enterprise-grade load balancing with health checks and traffic distribution

apiVersion: v1
kind: Service
metadata:
  name: regulens-ai-lb
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: load-balancer
  annotations:
    # AWS Load Balancer Controller annotations
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-scheme: "internet-facing"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: "/health"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval-seconds: "10"
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout-seconds: "5"
    service.beta.kubernetes.io/aws-load-balancer-healthy-threshold-count: "2"
    service.beta.kubernetes.io/aws-load-balancer-unhealthy-threshold-count: "3"
    
    # GCP Load Balancer annotations
    cloud.google.com/load-balancer-type: "External"
    cloud.google.com/backend-config: '{"default": "regulens-ai-backend-config"}'
    
    # Azure Load Balancer annotations
    service.beta.kubernetes.io/azure-load-balancer-internal: "false"
    service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path: "/health"
spec:
  type: LoadBalancer
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 3600
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: https
    port: 443
    targetPort: 8000
    protocol: TCP
  selector:
    app: regulens-ai

---
# Backend Configuration for GCP
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: regulens-ai-backend-config
  namespace: regulens-ai
spec:
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 2
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 8000
  connectionDraining:
    drainingTimeoutSec: 60
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600
  customRequestHeaders:
    headers:
    - "X-Forwarded-Proto:https"
    - "X-Real-IP:{client_ip}"

---
# Ingress for advanced routing and SSL termination
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: regulens-ai-ingress
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: ingress
  annotations:
    # NGINX Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/rate-limit-connections: "10"
    
    # SSL/TLS configuration
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;
spec:
  tls:
  - hosts:
    - api.regulens.ai
    - app.regulens.ai
    secretName: regulens-ai-tls
  rules:
  - host: api.regulens.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: regulens-ai-lb
            port:
              number: 80
  - host: app.regulens.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: regulens-ai-lb
            port:
              number: 80

---
# Service Monitor for Prometheus metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: regulens-ai-metrics
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: monitoring
spec:
  selector:
    matchLabels:
      app: regulens-ai
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
# Network Policy for security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: regulens-ai-network-policy
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: security
spec:
  podSelector:
    matchLabels:
      app: regulens-ai
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - podSelector:
        matchLabels:
          app: regulens-redis
    ports:
    - protocol: TCP
      port: 6379
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: regulens-redis
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 443   # HTTPS
    - protocol: TCP
      port: 53    # DNS
    - protocol: UDP
      port: 53    # DNS

---
# Custom Metrics API for HPA
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-config
  namespace: regulens-ai
  labels:
    app: regulens-ai
    component: metrics
data:
  metrics.yaml: |
    metrics:
      - name: http_requests_per_second
        query: 'rate(http_requests_total{job="regulens-ai"}[1m])'
        type: gauge
        
      - name: database_connections_active
        query: 'pg_stat_activity_count{job="postgres"}'
        type: gauge
        
      - name: cache_hit_rate
        query: 'redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total)'
        type: gauge
        
      - name: response_time_p95
        query: 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="regulens-ai"}[5m]))'
        type: gauge
        
      - name: error_rate
        query: 'rate(http_requests_total{job="regulens-ai",status=~"5.."}[5m]) / rate(http_requests_total{job="regulens-ai"}[5m])'
        type: gauge
