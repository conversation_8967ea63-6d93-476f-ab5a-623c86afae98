apiVersion: v1
kind: Secret
metadata:
  name: regulensai-secrets
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # Database credentials (base64 encoded)
  SUPABASE_URL: <base64-encoded-supabase-url>
  SUPABASE_ANON_KEY: <base64-encoded-supabase-anon-key>
  SUPABASE_SERVICE_ROLE_KEY: <base64-encoded-supabase-service-role-key>
  DATABASE_URL: <base64-encoded-database-url>
  
  # Encryption keys
  ENCRYPTION_KEY: <base64-encoded-32-character-encryption-key>
  JWT_SECRET: <base64-encoded-jwt-secret>
  
  # Redis password
  REDIS_PASSWORD: <base64-encoded-redis-password>
  
  # External API credentials
  REFINITIV_API_KEY: <base64-encoded-refinitiv-api-key>
  REFINITIV_USERNAME: <base64-encoded-refinitiv-username>
  REFINITIV_PASSWORD: <base64-encoded-refinitiv-password>
  
  EXPERIAN_CLIENT_ID: <base64-encoded-experian-client-id>
  EXPERIAN_CLIENT_SECRET: <base64-encoded-experian-client-secret>
  EXPERIAN_SUBSCRIBER_CODE: <base64-encoded-experian-subscriber-code>
  EXPERIAN_SUB_CODE: <base64-encoded-experian-sub-code>
  
  # Email service credentials
  SMTP_HOST: <base64-encoded-smtp-host>
  SMTP_PORT: <base64-encoded-smtp-port>
  SMTP_USERNAME: <base64-encoded-smtp-username>
  SMTP_PASSWORD: <base64-encoded-smtp-password>
  
  # SMS service credentials
  TWILIO_ACCOUNT_SID: <base64-encoded-twilio-account-sid>
  TWILIO_AUTH_TOKEN: <base64-encoded-twilio-auth-token>
  TWILIO_PHONE_NUMBER: <base64-encoded-twilio-phone-number>
  
  # Webhook signing secrets
  WEBHOOK_SIGNING_SECRET: <base64-encoded-webhook-signing-secret>

---
apiVersion: v1
kind: Secret
metadata:
  name: regulensai-tls
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: tls
type: kubernetes.io/tls
data:
  tls.crt: <base64-encoded-tls-certificate>
  tls.key: <base64-encoded-tls-private-key>

---
apiVersion: v1
kind: Secret
metadata:
  name: regulensai-registry
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: registry
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: <base64-encoded-docker-config>

---
# External secrets operator configuration (if using external secret management)
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-backend
  namespace: regulensai
spec:
  provider:
    vault:
      server: "https://vault.regulensai.com"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "regulensai"
          serviceAccountRef:
            name: "regulensai-vault-auth"

---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: regulensai-external-secrets
  namespace: regulensai
spec:
  refreshInterval: 15s
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: regulensai-vault-secrets
    creationPolicy: Owner
  data:
  - secretKey: database-url
    remoteRef:
      key: regulensai/database
      property: url
  - secretKey: encryption-key
    remoteRef:
      key: regulensai/encryption
      property: key
  - secretKey: jwt-secret
    remoteRef:
      key: regulensai/jwt
      property: secret
  - secretKey: refinitiv-api-key
    remoteRef:
      key: regulensai/refinitiv
      property: api_key
  - secretKey: experian-client-id
    remoteRef:
      key: regulensai/experian
      property: client_id
  - secretKey: experian-client-secret
    remoteRef:
      key: regulensai/experian
      property: client_secret

---
# Service account for Vault authentication
apiVersion: v1
kind: ServiceAccount
metadata:
  name: regulensai-vault-auth
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: vault-auth

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: regulensai-vault-auth
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
- kind: ServiceAccount
  name: regulensai-vault-auth
  namespace: regulensai

---
# Secret for monitoring credentials
apiVersion: v1
kind: Secret
metadata:
  name: regulensai-monitoring-secrets
  namespace: regulensai-monitoring
  labels:
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: secrets
type: Opaque
data:
  # Prometheus credentials
  PROMETHEUS_ADMIN_PASSWORD: <base64-encoded-prometheus-admin-password>
  
  # Grafana credentials
  GRAFANA_ADMIN_PASSWORD: <base64-encoded-grafana-admin-password>
  GRAFANA_SECRET_KEY: <base64-encoded-grafana-secret-key>
  
  # Jaeger credentials
  JAEGER_ADMIN_PASSWORD: <base64-encoded-jaeger-admin-password>
  
  # AlertManager credentials
  ALERTMANAGER_WEBHOOK_URL: <base64-encoded-alertmanager-webhook-url>
  SLACK_API_URL: <base64-encoded-slack-api-url>
  PAGERDUTY_INTEGRATION_KEY: <base64-encoded-pagerduty-integration-key>

---
# Template for creating secrets from external sources
apiVersion: v1
kind: Secret
metadata:
  name: regulensai-secrets-template
  namespace: regulensai
  annotations:
    template.openshift.io/expose-database_url: "{.data['DATABASE_URL']}"
    template.openshift.io/expose-encryption_key: "{.data['ENCRYPTION_KEY']}"
type: Opaque
stringData:
  # Template for environment-specific secrets
  DATABASE_URL: "************************************/database"
  ENCRYPTION_KEY: "your-32-character-encryption-key-here"
  JWT_SECRET: "your-jwt-secret-key-here"
  REDIS_PASSWORD: "your-redis-password-here"
  
  # External API credentials templates
  REFINITIV_API_KEY: "your-refinitiv-api-key"
  REFINITIV_USERNAME: "your-refinitiv-username"
  REFINITIV_PASSWORD: "your-refinitiv-password"
  
  EXPERIAN_CLIENT_ID: "your-experian-client-id"
  EXPERIAN_CLIENT_SECRET: "your-experian-client-secret"
  
  # Notification service credentials
  SMTP_HOST: "smtp.provider.com"
  SMTP_PORT: "587"
  SMTP_USERNAME: "your-smtp-username"
  SMTP_PASSWORD: "your-smtp-password"
  
  TWILIO_ACCOUNT_SID: "your-twilio-account-sid"
  TWILIO_AUTH_TOKEN: "your-twilio-auth-token"
  TWILIO_PHONE_NUMBER: "your-twilio-phone-number"
