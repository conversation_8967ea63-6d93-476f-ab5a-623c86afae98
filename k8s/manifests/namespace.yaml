apiVersion: v1
kind: Namespace
metadata:
  name: regulensai
  labels:
    name: regulensai
    environment: production
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: helm
  annotations:
    description: "RegulensAI compliance and risk management platform"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: Namespace
metadata:
  name: regulensai-staging
  labels:
    name: regulensai-staging
    environment: staging
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: helm
  annotations:
    description: "RegulensAI staging environment"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: Namespace
metadata:
  name: regulensai-monitoring
  labels:
    name: regulensai-monitoring
    environment: production
    app.kubernetes.io/name: monitoring
    app.kubernetes.io/component: observability
  annotations:
    description: "RegulensAI monitoring and observability stack"
    contact: "<EMAIL>"
