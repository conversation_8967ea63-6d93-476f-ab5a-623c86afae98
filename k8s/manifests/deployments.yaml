apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulensai-api
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: api
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: regulensai
      app.kubernetes.io/component: api
  template:
    metadata:
      labels:
        app.kubernetes.io/name: regulensai
        app.kubernetes.io/component: api
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: regulensai-api
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: api
        image: regulensai/api:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: ENVIRONMENT
        - name: API_HOST
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: API_HOST
        - name: API_PORT
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: API_PORT
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: DATABASE_URL
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: REDIS_HOST
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REDIS_PASSWORD
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: ENCRYPTION_KEY
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: JWT_SECRET
        envFrom:
        - configMapRef:
            name: regulensai-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      imagePullSecrets:
      - name: regulensai-registry

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulensai-notifications
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: notifications
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: regulensai
      app.kubernetes.io/component: notifications
  template:
    metadata:
      labels:
        app.kubernetes.io/name: regulensai
        app.kubernetes.io/component: notifications
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9091"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: regulensai-notifications
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: notifications
        image: regulensai/notifications:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8001
          protocol: TCP
        - name: metrics
          containerPort: 9091
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: ENVIRONMENT
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: DATABASE_URL
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: REDIS_HOST
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REDIS_PASSWORD
        - name: SMTP_HOST
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: SMTP_HOST
        - name: SMTP_USERNAME
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: SMTP_USERNAME
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: SMTP_PASSWORD
        - name: TWILIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: TWILIO_ACCOUNT_SID
        - name: TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: TWILIO_AUTH_TOKEN
        envFrom:
        - configMapRef:
            name: regulensai-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      imagePullSecrets:
      - name: regulensai-registry

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulensai-integrations
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: integrations
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: regulensai
      app.kubernetes.io/component: integrations
  template:
    metadata:
      labels:
        app.kubernetes.io/name: regulensai
        app.kubernetes.io/component: integrations
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9092"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: regulensai-integrations
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: integrations
        image: regulensai/integrations:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8002
          protocol: TCP
        - name: metrics
          containerPort: 9092
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: ENVIRONMENT
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: DATABASE_URL
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: REDIS_HOST
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REDIS_PASSWORD
        - name: REFINITIV_API_KEY
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REFINITIV_API_KEY
        - name: REFINITIV_USERNAME
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REFINITIV_USERNAME
        - name: REFINITIV_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REFINITIV_PASSWORD
        - name: EXPERIAN_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: EXPERIAN_CLIENT_ID
        - name: EXPERIAN_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: EXPERIAN_CLIENT_SECRET
        envFrom:
        - configMapRef:
            name: regulensai-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}
      imagePullSecrets:
      - name: regulensai-registry

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: regulensai-worker
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: worker
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: regulensai
      app.kubernetes.io/component: worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: regulensai
        app.kubernetes.io/component: worker
        app.kubernetes.io/version: "1.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9093"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: regulensai-worker
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: worker
        image: regulensai/worker:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: metrics
          containerPort: 9093
          protocol: TCP
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: ENVIRONMENT
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: DATABASE_URL
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: regulensai-config
              key: REDIS_HOST
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: regulensai-secrets
              key: REDIS_PASSWORD
        envFrom:
        - configMapRef:
            name: regulensai-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "125m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "ps aux | grep -v grep | grep worker"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      imagePullSecrets:
      - name: regulensai-registry
