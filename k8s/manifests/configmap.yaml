apiVersion: v1
kind: ConfigMap
metadata:
  name: regulensai-config
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: config
data:
  # Application Configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  
  # API Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_WORKERS: "4"
  API_TIMEOUT: "30"
  
  # Database Configuration
  DB_POOL_SIZE: "20"
  DB_MAX_OVERFLOW: "30"
  DB_POOL_TIMEOUT: "30"
  DB_POOL_RECYCLE: "3600"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_POOL_SIZE: "10"
  REDIS_TIMEOUT: "5"
  
  # External Data Provider Configuration
  OFAC_CACHE_HOURS: "12"
  OFAC_AUTO_UPDATE: "true"
  OFAC_UPDATE_INTERVAL_HOURS: "4"
  OFAC_BASE_URL: "https://www.treasury.gov/ofac/downloads"
  
  EU_SANCTIONS_CACHE_HOURS: "24"
  EU_SANCTIONS_AUTO_UPDATE: "true"
  EU_SANCTIONS_UPDATE_INTERVAL_HOURS: "6"
  
  UN_SANCTIONS_CACHE_HOURS: "24"
  UN_SANCTIONS_AUTO_UPDATE: "true"
  UN_SANCTIONS_UPDATE_INTERVAL_HOURS: "6"
  
  # Notification Configuration
  NOTIFICATION_BATCH_SIZE: "100"
  NOTIFICATION_MAX_CONCURRENT_BATCHES: "10"
  NOTIFICATION_RETRY_ATTEMPTS: "3"
  NOTIFICATION_RETRY_DELAY: "5"
  
  # GRC Integration Configuration
  GRC_SYNC_INTERVAL_HOURS: "6"
  GRC_BATCH_SIZE: "500"
  GRC_TIMEOUT_SECONDS: "60"
  
  # Monitoring Configuration
  METRICS_ENABLED: "true"
  METRICS_PORT: "9090"
  TRACING_ENABLED: "true"
  JAEGER_ENDPOINT: "http://jaeger-collector:14268/api/traces"
  
  # Feature Flags
  FEATURE_ENHANCED_NOTIFICATIONS: "true"
  FEATURE_EXTERNAL_DATA_PROVIDERS: "true"
  FEATURE_GRC_CONNECTORS: "true"
  FEATURE_ADVANCED_ANALYTICS: "false"
  
  # Rate Limiting
  RATE_LIMIT_REQUESTS_PER_MINUTE: "1000"
  RATE_LIMIT_BURST_SIZE: "100"
  
  # Security Configuration
  CORS_ORIGINS: "https://app.regulensai.com,https://admin.regulensai.com"
  ALLOWED_HOSTS: "api.regulensai.com,*.regulensai.com"
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: regulensai-nginx-config
  namespace: regulensai
  labels:
    app.kubernetes.io/name: regulensai
    app.kubernetes.io/component: nginx
data:
  nginx.conf: |
    upstream api_backend {
        least_conn;
        server regulensai-api:8000 max_fails=3 fail_timeout=30s;
    }
    
    upstream notification_backend {
        least_conn;
        server regulensai-notifications:8001 max_fails=3 fail_timeout=30s;
    }
    
    upstream integration_backend {
        least_conn;
        server regulensai-integrations:8002 max_fails=3 fail_timeout=30s;
    }
    
    server {
        listen 80;
        server_name api.regulensai.com;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name api.regulensai.com;
        
        # SSL Configuration
        ssl_certificate /etc/ssl/certs/tls.crt;
        ssl_certificate_key /etc/ssl/private/tls.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Security Headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;
        
        # Rate Limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req zone=api burst=20 nodelay;
        
        # API Routes
        location /api/v1/notifications/ {
            proxy_pass http://notification_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 60s;
            proxy_read_timeout 60s;
            proxy_connect_timeout 10s;
        }
        
        location /api/v1/integrations/ {
            proxy_pass http://integration_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 120s;
            proxy_read_timeout 120s;
            proxy_connect_timeout 10s;
        }
        
        location /api/v1/ {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 30s;
            proxy_read_timeout 30s;
            proxy_connect_timeout 10s;
        }
        
        # Health Check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Metrics endpoint (internal only)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
            proxy_pass http://api_backend/metrics;
        }
    }
  
  default.conf: |
    # Default server block to handle unknown hosts
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name _;
        
        ssl_certificate /etc/ssl/certs/tls.crt;
        ssl_certificate_key /etc/ssl/private/tls.key;
        
        return 444;
    }

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: regulensai-redis-config
  namespace: regulensai
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
data:
  redis.conf: |
    # Redis Configuration for RegulensAI
    
    # Network
    bind 0.0.0.0
    port 6379
    tcp-backlog 511
    timeout 0
    tcp-keepalive 300
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    
    # Snapshotting
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir ./
    
    # Replication
    replica-serve-stale-data yes
    replica-read-only yes
    repl-diskless-sync no
    repl-diskless-sync-delay 5
    repl-disable-tcp-nodelay no
    replica-priority 100
    
    # Security
    requirepass ${REDIS_PASSWORD}
    
    # Memory Management
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    maxmemory-samples 5
    
    # Lazy Freeing
    lazyfree-lazy-eviction no
    lazyfree-lazy-expire no
    lazyfree-lazy-server-del no
    replica-lazy-flush no
    
    # Append Only Mode
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    aof-load-truncated yes
    aof-use-rdb-preamble yes
    
    # Lua Scripting
    lua-time-limit 5000
    
    # Slow Log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Latency Monitor
    latency-monitor-threshold 100
    
    # Event Notification
    notify-keyspace-events ""
    
    # Advanced Config
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    list-compress-depth 0
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    hll-sparse-max-bytes 3000
    stream-node-max-bytes 4096
    stream-node-max-entries 100
    activerehashing yes
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    hz 10
    dynamic-hz yes
    aof-rewrite-incremental-fsync yes
    rdb-save-incremental-fsync yes
