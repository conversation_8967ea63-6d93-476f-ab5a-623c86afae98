apiVersion: apps/v1
kind: Deployment
metadata:
  name: training-portal
  namespace: regulensai
  labels:
    app: training-portal
    component: web
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: training-portal
  template:
    metadata:
      labels:
        app: training-portal
        component: web
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: training-portal
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: training-portal
        image: regulensai/training-portal:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: jwt-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: encryption-key
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: aws-access-key
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: training-portal-secrets
              key: aws-secret-key
        - name: S3_BUCKET_NAME
          valueFrom:
            configMapKeyRef:
              name: training-portal-config
              key: s3-bucket-name
        - name: SMTP_HOST
          valueFrom:
            configMapKeyRef:
              name: training-portal-config
              key: smtp-host
        - name: SMTP_PORT
          valueFrom:
            configMapKeyRef:
              name: training-portal-config
              key: smtp-port
        - name: PROMETHEUS_MULTIPROC_DIR
          value: "/tmp/prometheus"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: prometheus-metrics
          mountPath: /tmp/prometheus
        - name: training-content
          mountPath: /app/training_content
          readOnly: true
        - name: certificate-templates
          mountPath: /app/assets
          readOnly: true
      - name: nginx-sidecar
        image: nginx:1.21-alpine
        ports:
        - name: nginx
          containerPort: 80
          protocol: TCP
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
          readOnly: true
        - name: static-files
          mountPath: /usr/share/nginx/html
          readOnly: true
      volumes:
      - name: tmp
        emptyDir: {}
      - name: prometheus-metrics
        emptyDir: {}
      - name: nginx-config
        configMap:
          name: training-portal-nginx-config
      - name: static-files
        configMap:
          name: training-portal-static-files
      - name: training-content
        configMap:
          name: training-portal-content
      - name: certificate-templates
        configMap:
          name: training-portal-templates
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - training-portal
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "training-workload"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: training-portal-service
  namespace: regulensai
  labels:
    app: training-portal
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-west-2:************:certificate/12345678-1234-1234-1234-************"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: nginx
    protocol: TCP
  - name: https
    port: 443
    targetPort: nginx
    protocol: TCP
  selector:
    app: training-portal

---
apiVersion: v1
kind: Service
metadata:
  name: training-portal-internal
  namespace: regulensai
  labels:
    app: training-portal
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: training-portal

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: training-portal-hpa
  namespace: regulensai
  labels:
    app: training-portal
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: training-portal
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: training_portal_active_users
      target:
        type: AverageValue
        averageValue: "50"
  - type: Pods
    pods:
      metric:
        name: training_portal_request_rate
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: training-portal-pdb
  namespace: regulensai
  labels:
    app: training-portal
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: training-portal

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: training-portal
  namespace: regulensai
  labels:
    app: training-portal
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/TrainingPortalServiceRole

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: training-portal
  namespace: regulensai
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: training-portal
  namespace: regulensai
subjects:
- kind: ServiceAccount
  name: training-portal
  namespace: regulensai
roleRef:
  kind: Role
  name: training-portal
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: training-portal-config
  namespace: regulensai
  labels:
    app: training-portal
data:
  s3-bucket-name: "regulensai-training-content"
  smtp-host: "smtp.amazonaws.com"
  smtp-port: "587"
  max-upload-size: "100MB"
  session-timeout: "3600"
  certificate-validity-days: "365"
  analytics-retention-days: "90"
  cache-ttl: "300"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: training-portal-nginx-config
  namespace: regulensai
  labels:
    app: training-portal
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
        
        upstream training_portal {
            server localhost:8080;
            keepalive 32;
        }
        
        server {
            listen 80;
            server_name _;
            
            # Security headers
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
            
            # Static files
            location /static/ {
                root /usr/share/nginx/html;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # Health checks
            location /health {
                proxy_pass http://training_portal;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # API endpoints
            location /api/ {
                proxy_pass http://training_portal;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # Training portal app
            location / {
                proxy_pass http://training_portal;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }
