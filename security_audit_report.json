{"audit_timestamp": "2025-07-31 09:53:41.396948+00:00", "project_root": ".", "summary": {"total_issues": 168, "security_score": 0, "critical_issues": 57, "high_issues": 16, "medium_issues": 81, "low_issues": 14}, "findings_by_severity": {"critical": [{"category": "hardcoded_secrets", "file_path": "ultimate_final_validation.py", "line_number": 20, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "final_validation.py", "line_number": 17, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "clean_deployment_check.py", "line_number": 18, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "emergency_schema_fix.py", "line_number": 18, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "fix_schema_issues.py", "line_number": 18, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "final_fix_and_validate.py", "line_number": 19, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "final_comprehensive_validation.py", "line_number": 21, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "fix_users_table.py", "line_number": 16, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "critical_schema_audit.py", "line_number": 20, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "deploy_schema_to_supabase.py", "line_number": 21, "description": "Potential hardcoded password detected", "evidence": "password': 'mdRh6u26EeogT2M0'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/conftest.py", "line_number": 262, "description": "Potential hardcoded password detected", "evidence": "password': 'testpassword123'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/test_configuration_validation.py", "line_number": 50, "description": "Potential hardcoded database_url detected", "evidence": "mysql://user:pass@host/db", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "scripts/security_audit.py", "line_number": 57, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://[^:]+:[^@]+@[^/]+/[^", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "scripts/security_audit.py", "line_number": 58, "description": "Potential hardcoded database_url detected", "evidence": "mysql://[^:]+:[^@]+@[^/]+/[^", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "scripts/security_audit.py", "line_number": 59, "description": "Potential hardcoded database_url detected", "evidence": "mongodb://[^:]+:[^@]+@[^/]+/[^", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/config.py", "line_number": 642, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://username:password@host:port/database", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/config.py", "line_number": 655, "description": "Potential hardcoded database_url detected", "evidence": "************************************/db", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/config.py", "line_number": 662, "description": "Potential hardcoded database_url detected", "evidence": "********************************/database_name", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/config.py", "line_number": 669, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://username:password@host:port/database", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "security/penetration_testing/security_scanner.py", "line_number": 262, "description": "Potential hardcoded password detected", "evidence": "password\": \"testpass\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_auth.py", "line_number": 27, "description": "Potential hardcoded password detected", "evidence": "password = \"testpassword123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_auth.py", "line_number": 36, "description": "Potential hardcoded password detected", "evidence": "password = \"testpassword123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_auth.py", "line_number": 43, "description": "Potential hardcoded password detected", "evidence": "password = \"testpassword123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_auth.py", "line_number": 44, "description": "Potential hardcoded password detected", "evidence": "password = \"wrongpassword\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_auth.py", "line_number": 51, "description": "Potential hardcoded password detected", "evidence": "password = \"testpassword123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_external_data_integration.py", "line_number": 419, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_grc_integration.py", "line_number": 34, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_grc_integration.py", "line_number": 68, "description": "Potential hardcoded password detected", "evidence": "password': 'wrong_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_grc_integration.py", "line_number": 215, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_credential_management.py", "line_number": 24, "description": "Potential hardcoded password detected", "evidence": "PASSWORD': 'test-password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_credential_management.py", "line_number": 305, "description": "Potential hardcoded password detected", "evidence": "password = \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_core_banking_integration.py", "line_number": 186, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/unit/test_core_banking_integration.py", "line_number": 257, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/security/test_authentication_security.py", "line_number": 469, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password_123'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/security/test_owasp_top10.py", "line_number": 423, "description": "Potential hardcoded password detected", "evidence": "password': 'test_password_123'", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/test_api_workflows.py", "line_number": 36, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/test_api_workflows.py", "line_number": 194, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/test_api_workflows.py", "line_number": 314, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/test_api_workflows.py", "line_number": 468, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/test_api_workflows.py", "line_number": 512, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/integration/fixtures.py", "line_number": 104, "description": "Potential hardcoded password detected", "evidence": "password = \"test_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "tests/load_testing/locustfile.py", "line_number": 36, "description": "Potential hardcoded password detected", "evidence": "password\": \"test_password_123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/api/swagger_config.py", "line_number": 57, "description": "Potential hardcoded password detected", "evidence": "password\": \"your_password\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/ui/testing_portal/app.py", "line_number": 152, "description": "Potential hardcoded password detected", "evidence": "password\": \"TestPass123!\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/ui/testing_portal/app.py", "line_number": 163, "description": "Potential hardcoded password detected", "evidence": "password\": \"TestPass123!\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "core_infra/ui/testing_portal/app.py", "line_number": 537, "description": "Potential hardcoded password detected", "evidence": "password\": \"TestPass123!\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "ui/src/components/operations/EnhancedDeploymentGuide.js", "line_number": 304, "description": "Potential hardcoded database_url detected", "evidence": "*******************************************************************************************/regulens...", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "ui/src/components/operations/DeploymentGuide.js", "line_number": 154, "description": "Potential hardcoded password detected", "evidence": "password\": \"admin123\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "oneclickinstall.sh", "line_number": 279, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://postgres:<EMAIL>:5432/postgres", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "oneclickinstall.sh", "line_number": 438, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://postgres:<EMAIL>:5432/postgres", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "oneclickinstall.sh", "line_number": 441, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://username:password@host:port/database", "recommendation": "Move database_url to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "oneclickinstall.sh", "line_number": 491, "description": "Potential hardcoded password detected", "evidence": "PASSWORD=\"${GRAFANA_ADMIN_PASSWORD:-$(generate_secure_key | head -c 16)}\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "monitoring/deploy-monitoring.sh", "line_number": 10, "description": "Potential hardcoded password detected", "evidence": "PASSWORD=\"${GRAFANA_ADMIN_PASSWORD:-admin123}\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "monitoring/deploy-monitoring.sh", "line_number": 15, "description": "Potential hardcoded password detected", "evidence": "PASSWORD=\"${SMTP_PASSWORD:-}\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "monitoring/deploy-monitoring.sh", "line_number": 98, "description": "Potential hardcoded password detected", "evidence": "password=\"$SMTP_PASSWORD\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "monitoring/deploy-monitoring.sh", "line_number": 99, "description": "Potential hardcoded password detected", "evidence": "Password=\"$GRAFANA_ADMIN_PASSWORD\"", "recommendation": "Move password to environment variables or secure configuration"}, {"category": "hardcoded_secrets", "file_path": "monitoring/deploy-monitoring.sh", "line_number": 168, "description": "Potential hardcoded database_url detected", "evidence": "postgresql://\\${POSTGRES_USER}:\\${POSTGRES_PASSWORD}@\\${POSTGRES_HOST}:5432/\\${POSTGRES_DB}?sslmode=...", "recommendation": "Move database_url to environment variables or secure configuration"}], "high": [{"category": "vulnerability", "file_path": "scripts/security_audit.py", "line_number": 77, "description": "Potential command injection vulnerability", "evidence": "shell=True.*\\+", "recommendation": "Validate and sanitize input, avoid shell=True, use subprocess with list arguments"}, {"category": "default_credentials", "file_path": "docker-compose.yml", "line_number": 271, "description": "Default password detected", "evidence": "- GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 31, "description": "Default password detected", "evidence": "REDIS_PASSWORD=your-secure-redis-password-change-this", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 67, "description": "Default password detected", "evidence": "REFINITIV_PASSWORD=your-refinitiv-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 72, "description": "Default password detected", "evidence": "DOWJONES_PASSWORD=your-dowjones-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 86, "description": "Default password detected", "evidence": "ARCHER_PASSWORD=your-archer-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 102, "description": "Default password detected", "evidence": "SERVICENOW_PASSWORD=your-servicenow-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 110, "description": "Default password detected", "evidence": "METRICSTREAM_PASSWORD=your-metricstream-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 120, "description": "Default password detected", "evidence": "T24_PASSWORD=your-t24-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 129, "description": "Default password detected", "evidence": "FLEXCUBE_PASSWORD=your-flexcube-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 138, "description": "Default password detected", "evidence": "FINACLE_PASSWORD=your-finacle-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 148, "description": "Default password detected", "evidence": "REFINITIV_PASSWORD=your-refinitiv-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 183, "description": "Default password detected", "evidence": "ARCHER_PASSWORD=your-archer-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 192, "description": "Default password detected", "evidence": "SERVICENOW_PASSWORD=your-servicenow-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 217, "description": "Default password detected", "evidence": "SMTP_PASSWORD=your-smtp-password", "recommendation": "Change default passwords before deployment"}, {"category": "default_credentials", "file_path": ".env.example", "line_number": 249, "description": "Default password detected", "evidence": "GRAFANA_ADMIN_PASSWORD=your-secure-grafana-password", "recommendation": "Change default passwords before deployment"}], "medium": [{"category": "vulnerability", "file_path": "tests/security/test_training_security.py", "line_number": 123, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_training_security.py", "line_number": 123, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_training_security.py", "line_number": 123, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 298, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 298, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 298, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 300, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 300, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "tests/security/test_owasp_top10.py", "line_number": 300, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "core_infra/database/query_optimizer.py", "line_number": 138, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ai/embeddings.py", "line_number": 212, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/api/file_security.py", "line_number": 115, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/monitoring/apm_database.py", "line_number": 135, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/performance/optimization.py", "line_number": 199, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/caching/redis_cache_manager.py", "line_number": 414, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/caching/redis_cache_manager.py", "line_number": 470, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/caching/redis_cache_manager.py", "line_number": 482, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/services/training_cache_service.py", "line_number": 52, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ui/documentation_portal/app.py", "line_number": 124, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ui/documentation_portal/app.py", "line_number": 125, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ui/documentation_portal/app.py", "line_number": 310, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ui/documentation_portal/app.py", "line_number": 541, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/services/regulatory_monitor/monitor.py", "line_number": 481, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/services/integrations/document_management_integration.py", "line_number": 465, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/services/notifications/alert_manager.py", "line_number": 388, "description": "Potential weak crypto vulnerability", "evidence": "md5(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/contexts/AuthContext.js", "line_number": 127, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/contexts/ThemeContext.js", "line_number": 4, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/DashboardLayout.js", "line_number": 37, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/ProtectedRoute.js", "line_number": 4, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/theme/index.js", "line_number": 352, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/hooks/useResponsive.js", "line_number": 176, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/hooks/useResponsive.js", "line_number": 183, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/pages/LoginPage.js", "line_number": 24, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/RegulatoryMonitoring.js", "line_number": 161, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/pages/RegulatoryMonitoring.js", "line_number": 162, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 52, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 53, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 54, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 55, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 56, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 57, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 58, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 59, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 60, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/Operations.js", "line_number": 61, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/pages/UserManagement.js", "line_number": 113, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/pages/UserManagement.js", "line_number": 114, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/pages/UserManagement.js", "line_number": 115, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/documentation/DocumentationPortal.js", "line_number": 40, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/documentation/DocumentationPortal.js", "line_number": 41, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/documentation/index.js", "line_number": 10, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/documentation/index.js", "line_number": 13, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/ConfigurationValidation.js", "line_number": 37, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/ConfigurationValidation.js", "line_number": 37, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/ConfigurationValidation.js", "line_number": 38, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/ConfigurationValidation.js", "line_number": 39, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/ConfigurationValidation.js", "line_number": 40, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/TroubleshootingGuide.js", "line_number": 287, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/TroubleshootingGuide.js", "line_number": 288, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/operations/TroubleshootingGuide.js", "line_number": 289, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveTable.js", "line_number": 33, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveTable.js", "line_number": 33, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveTable.js", "line_number": 67, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveTable.js", "line_number": 79, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "ui/src/components/common/LoadingComponents.js", "line_number": 14, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/LoadingComponents.js", "line_number": 14, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveContainer.js", "line_number": 3, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveContainer.js", "line_number": 3, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveNavigation.js", "line_number": 40, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveNavigation.js", "line_number": 40, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveNavigation.js", "line_number": 41, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "ui/src/components/common/ResponsiveNavigation.js", "line_number": 41, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "frontend/src/services/api/disasterRecovery.ts", "line_number": 374, "description": "Potential weak crypto vulnerability", "evidence": "des(", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "oneclickinstall.sh", "line_number": 789, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "oneclickinstall.sh", "line_number": 796, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "vulnerability", "file_path": "oneclickinstall.sh", "line_number": 811, "description": "Potential weak crypto vulnerability", "evidence": "des (", "recommendation": "Use strong cryptographic algorithms (SHA-256, AES-256, RSA-2048+)"}, {"category": "vulnerability", "file_path": "core_infra/ui/start_ui_portals.sh", "line_number": 90, "description": "Potential path traversal vulnerability", "evidence": "../", "recommendation": "Validate file paths, use os.path.join(), restrict access to allowed directories"}, {"category": "file_permissions", "file_path": ".env.backup.1753701994", "line_number": 0, "description": "Sensitive file is world-readable", "evidence": "File permissions: 644", "recommendation": "Restrict file permissions to owner only (600 or 700)"}, {"category": "file_permissions", "file_path": ".env", "line_number": 0, "description": "Sensitive file is world-readable", "evidence": "File permissions: 644", "recommendation": "Restrict file permissions to owner only (600 or 700)"}, {"category": "file_permissions", "file_path": "core_infra/config.py", "line_number": 0, "description": "Sensitive file is world-readable", "evidence": "File permissions: 664", "recommendation": "Restrict file permissions to owner only (600 or 700)"}, {"category": "file_permissions", "file_path": "core_infra/api/swagger_config.py", "line_number": 0, "description": "Sensitive file is world-readable", "evidence": "File permissions: 664", "recommendation": "Restrict file permissions to owner only (600 or 700)"}], "low": [{"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 94, "description": "Placeholder comment detected: \"TODO\"", "evidence": "r'TODO',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 95, "description": "Placeholder comment detected: \"FIXME\"", "evidence": "r'FIXME',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 96, "description": "Placeholder comment detected: \"XXX\"", "evidence": "r'XXX',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 97, "description": "Placeholder comment detected: \"HACK\"", "evidence": "r'HACK',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 98, "description": "Placeholder comment detected: \"NOTE FOR PROD\"", "evidence": "r'NOTE FOR PROD',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 99, "description": "Placeholder comment detected: \"REMOVE BEFORE DEPLOY\"", "evidence": "r'REMOVE BEFORE DEPLOY',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 100, "description": "Placeholder comment detected: \"DISABLED FOR PROD\"", "evidence": "r'DISABLED FOR PROD',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 101, "description": "Placeholder comment detected: \"ENABLE IN PROD\"", "evidence": "r'ENABLE IN PROD',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 102, "description": "Placeholder comment detected: \"# stub\"", "evidence": "r'# stub',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 103, "description": "Placeholder comment detected: \"# placeholder\"", "evidence": "r'# placeholder',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 104, "description": "Placeholder comment detected: \"# not implemented\"", "evidence": "r'# not implemented',", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "scripts/security_audit.py", "line_number": 225, "description": "Placeholder comment detected: \"TODO\"", "evidence": "\"\"\"Scan for placeholder comments like TODO, FIXME, etc.\"\"\"", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "tests/security/test_training_security.py", "line_number": 459, "description": "Placeholder comment detected: \"# placeholder\"", "evidence": "pass  # Placeholder for actual API testing", "recommendation": "Implement the required functionality or remove the placeholder comment."}, {"category": "placeholder_comment", "file_path": "tests/security/test_training_security.py", "line_number": 466, "description": "Placeholder comment detected: \"# placeholder\"", "evidence": "pass  # Placeholder for rate limiting tests", "recommendation": "Implement the required functionality or remove the placeholder comment."}]}, "findings_by_category": {"hardcoded_secrets": 57, "vulnerability": 78, "placeholder_comment": 14, "file_permissions": 4, "default_credentials": 15}, "recommendations": ["Implement proper secrets management using environment variables or secret management services", "Conduct regular security code reviews and implement secure coding practices", "Review and fix file permissions for sensitive files", "Change all default passwords and credentials before deployment", "Implement automated security scanning in CI/CD pipeline", "Regular security training for development team", "Implement dependency vulnerability scanning", "Set up security monitoring and alerting"]}