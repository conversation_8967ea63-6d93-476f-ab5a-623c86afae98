================================================================================
REGULENSAI SCHEMA DEPLOYMENT REPORT
================================================================================
Deployment Time: 2025-07-31 07:55:51
Target Database: Supabase PostgreSQL
Schema File: core_infra/database/schema.sql

DEPLOYMENT SUMMARY:
----------------------------------------
Total Errors: 174
Total Warnings: 0

CREATED OBJECTS:
----------------------------------------
Tables: 119
Indexes: 324
Constraints: 106
Functions: 0
Triggers: 0

VALIDATION RESULTS:
----------------------------------------
total_tables: 118
core_tables_found: 3
total_indexes: 444
validation_successful: True

ERRORS ENCOUNTERED:
----------------------------------------
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.user_permissions ADD CONSTRAINT IF NOT EXISTS unique_user_permission UNIQUE (user...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_user_credentials_updated_at BEFORE UPDATE ON public.user_credentials FOR EACH ...
• SQL execution error: syntax error at or near "CASE"
• Failed statement: CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_success_rate ON public.notifications (tena...
• SQL execution error: syntax error at or near "/"
• Failed statement: CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_grc_records_age ON public.grc_records (tenant_id, EXTRAC...
• SQL execution error: unterminated dollar-quoted string at or near "$$ BEGIN NEW.updated_at = now();"
• Failed statement: CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = n...
• SQL execution error: syntax error at or near "RETURN"
• Failed statement: RETURN NEW;...
• SQL execution error: unterminated dollar-quoted string at or near "$$ language 'plpgsql';"
• Failed statement: $$ language 'plpgsql';...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON public.tenants FOR EACH ROW EXECUTE FUNCTI...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION u...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON public.customers FOR EACH ROW EXECUTE FU...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_compliance_programs_updated_at BEFORE UPDATE ON public.compliance_programs FOR...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_compliance_requirements_updated_at BEFORE UPDATE ON public.compliance_requirem...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_compliance_tasks_updated_at BEFORE UPDATE ON public.compliance_tasks FOR EACH ...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_regulatory_impact_assessments_updated_at BEFORE UPDATE ON public.regulatory_im...
• SQL execution error: relation "public.credentials" does not exist
• Failed statement: CREATE TRIGGER update_credentials_updated_at BEFORE UPDATE ON public.credentials FOR EACH ROW EXECUT...
• SQL execution error: relation "public.credential_rotation_schedule" does not exist
• Failed statement: CREATE TRIGGER update_credential_rotation_schedule_updated_at BEFORE UPDATE ON public.credential_rot...
• SQL execution error: relation "public.service_account_configurations" does not exist
• Failed statement: CREATE TRIGGER update_service_account_configurations_updated_at BEFORE UPDATE ON public.service_acco...
• SQL execution error: relation "public.external_service_endpoints" does not exist
• Failed statement: CREATE TRIGGER update_external_service_endpoints_updated_at BEFORE UPDATE ON public.external_service...
• SQL execution error: relation "public.notification_templates" does not exist
• Failed statement: CREATE TRIGGER update_notification_templates_updated_at BEFORE UPDATE ON public.notification_templat...
• SQL execution error: relation "public.user_notification_preferences" does not exist
• Failed statement: CREATE TRIGGER update_user_notification_preferences_updated_at BEFORE UPDATE ON public.user_notifica...
• SQL execution error: relation "public.tenant_notification_preferences" does not exist
• Failed statement: CREATE TRIGGER update_tenant_notification_preferences_updated_at BEFORE UPDATE ON public.tenant_noti...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.credentials ADD CONSTRAINT IF NOT EXISTS unique_tenant_service_type UNIQUE (tenan...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.service_account_configurations ADD CONSTRAINT IF NOT EXISTS unique_tenant_service...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.notification_templates ADD CONSTRAINT IF NOT EXISTS unique_tenant_template UNIQUE...
• SQL execution error: column "kyc_status" does not exist
• Failed statement: ALTER TABLE public.customers ADD CONSTRAINT chk_kyc_status CHECK (kyc_status IN ('verified', 'reject...
• SQL execution error: column "priority" does not exist
• Failed statement: ALTER TABLE public.compliance_tasks ADD CONSTRAINT chk_task_priority CHECK (priority IN ('critical',...
• SQL execution error: column "status" does not exist
• Failed statement: ALTER TABLE public.compliance_tasks ADD CONSTRAINT chk_task_status CHECK (status IN ('assigned', 'in...
• SQL execution error: column "language" does not exist
• Failed statement: ALTER TABLE public.notification_templates ADD CONSTRAINT chk_notification_language CHECK (language I...
• SQL execution error: column "country_code" of relation "regulatory_sources" does not exist
• Failed statement: INSERT INTO public.regulatory_sources (name, type, country_code, jurisdiction, website_url, monitori...
• SQL execution error: column "status" does not exist
HINT:  Perhaps you meant to reference the column "compliance_tasks.tags".
• Failed statement: ALTER TABLE public.compliance_tasks ADD CONSTRAINT chk_task_status CHECK (status IN ('draft', 'assig...
• SQL execution error: column "risk_category" does not exist
• Failed statement: ALTER TABLE public.customer_risk_scores ADD CONSTRAINT chk_customer_risk_category CHECK (risk_catego...
• SQL execution error: column "priority_level" does not exist
• Failed statement: ALTER TABLE public.regulatory_intelligence ADD CONSTRAINT chk_intelligence_priority CHECK (priority_...
• SQL execution error: column "status" does not exist
• Failed statement: ALTER TABLE public.integration_systems ADD CONSTRAINT chk_integration_status CHECK (status IN ('acti...
• SQL execution error: column "residual_risk_rating" does not exist
• Failed statement: ALTER TABLE public.grc_risk_registers ADD CONSTRAINT chk_grc_risk_rating CHECK (inherent_risk_rating...
• SQL execution error: column "data_format" does not exist
• Failed statement: ALTER TABLE public.external_data_sources ADD CONSTRAINT chk_external_data_format CHECK (data_format ...
• SQL execution error: column "lifecycle_stage" does not exist
• Failed statement: ALTER TABLE public.document_lifecycle ADD CONSTRAINT chk_document_lifecycle_stage CHECK (lifecycle_s...
• SQL execution error: column "experiment_status" does not exist
• Failed statement: ALTER TABLE public.ml_experiments ADD CONSTRAINT chk_ml_experiment_status CHECK (experiment_status I...
• SQL execution error: column "automation_level" does not exist
• Failed statement: ALTER TABLE public.automation_workflows ADD CONSTRAINT chk_automation_level CHECK (automation_level ...
• SQL execution error: column "resolution_status" does not exist
• Failed statement: ALTER TABLE public.chatbot_conversations ADD CONSTRAINT chk_resolution_status CHECK (resolution_stat...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.enhanced_monitoring ADD CONSTRAINT IF NOT EXISTS unique_customer_monitoring UNIQU...
• SQL execution error: unterminated dollar-quoted string at or near "$$ BEGIN NEW.updated_at = now();"
• Failed statement: CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = n...
• SQL execution error: syntax error at or near "RETURN"
• Failed statement: RETURN NEW;...
• SQL execution error: unterminated dollar-quoted string at or near "$$ language 'plpgsql';"
• Failed statement: $$ language 'plpgsql';...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON public.tenants FOR EACH ROW EXECUTE FUNCTI...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION u...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_user_credentials_updated_at BEFORE UPDATE ON public.user_credentials FOR EACH ...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON public.user_sessions FOR EACH ROW EX...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON public.customers FOR EACH ROW EXECUTE FU...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON public.transactions FOR EACH ROW EXEC...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON public.notifications FOR EACH ROW EX...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_alerts_updated_at BEFORE UPDATE ON public.alerts FOR EACH ROW EXECUTE FUNCTION...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_compliance_programs_updated_at BEFORE UPDATE ON public.compliance_programs FOR...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_compliance_requirements_updated_at BEFORE UPDATE ON public.compliance_requirem...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON public.tasks FOR EACH ROW EXECUTE FUNCTION u...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_screening_tasks_updated_at BEFORE UPDATE ON public.screening_tasks FOR EACH RO...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_enhanced_monitoring_updated_at BEFORE UPDATE ON public.enhanced_monitoring FOR...
• SQL execution error: unterminated dollar-quoted string at or near "$$ BEGIN IF NEW.tenant_id != (current_setting('app.current_tenant_id', true))::uuid THEN RAISE EXCEPTION 'Access denied: Invalid tenant ID';"
• Failed statement: CREATE OR REPLACE FUNCTION validate_tenant_access() RETURNS TRIGGER AS $$ BEGIN IF NEW.tenant_id != ...
• SQL execution error: syntax error at or near "IF"
• Failed statement: END IF;...
• SQL execution error: syntax error at or near "RETURN"
• Failed statement: RETURN NEW;...
• SQL execution error: unterminated dollar-quoted string at or near "$$ language 'plpgsql';"
• Failed statement: $$ language 'plpgsql';...
• SQL execution error: function validate_tenant_access() does not exist
• Failed statement: CREATE TRIGGER validate_tenant_access_users BEFORE INSERT OR UPDATE ON public.users FOR EACH ROW EXE...
• SQL execution error: function validate_tenant_access() does not exist
• Failed statement: CREATE TRIGGER validate_tenant_access_customers BEFORE INSERT OR UPDATE ON public.customers FOR EACH...
• SQL execution error: function validate_tenant_access() does not exist
• Failed statement: CREATE TRIGGER validate_tenant_access_transactions BEFORE INSERT OR UPDATE ON public.transactions FO...
• SQL execution error: column "name" of relation "permissions" does not exist
• Failed statement: INSERT INTO public.permissions (name, description, category, resource, action) VALUES ('auth.login',...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.training_sections ADD CONSTRAINT IF NOT EXISTS unique_module_section_code UNIQUE(...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.training_assessments ADD CONSTRAINT IF NOT EXISTS unique_module_assessment_code U...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.training_enrollments ADD CONSTRAINT IF NOT EXISTS unique_user_module UNIQUE(user_...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.training_discussion_votes ADD CONSTRAINT IF NOT EXISTS unique_discussion_user_vot...
• SQL execution error: column "difficulty_level" does not exist
• Failed statement: ALTER TABLE public.training_modules ADD CONSTRAINT chk_training_module_difficulty CHECK (difficulty_...
• SQL execution error: column "content_type" does not exist
• Failed statement: ALTER TABLE public.training_modules ADD CONSTRAINT chk_training_module_content_type CHECK (content_t...
• SQL execution error: column "passing_score" does not exist
• Failed statement: ALTER TABLE public.training_assessments ADD CONSTRAINT chk_training_assessment_passing_score CHECK (...
• SQL execution error: column "completion_percentage" does not exist
• Failed statement: ALTER TABLE public.training_enrollments ADD CONSTRAINT chk_training_enrollment_completion_percentage...
• SQL execution error: column "status" does not exist
• Failed statement: ALTER TABLE public.training_assessment_attempts ADD CONSTRAINT chk_training_assessment_attempt_statu...
• SQL execution error: column "score" does not exist
• Failed statement: ALTER TABLE public.training_assessment_attempts ADD CONSTRAINT chk_training_assessment_attempt_score...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_training_modules_updated_at BEFORE UPDATE ON public.training_modules FOR EACH ...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_training_sections_updated_at BEFORE UPDATE ON public.training_sections FOR EAC...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_training_assessments_updated_at BEFORE UPDATE ON public.training_assessments F...
• SQL execution error: function update_updated_at_column() does not exist
• Failed statement: CREATE TRIGGER update_training_discussions_updated_at BEFORE UPDATE ON public.training_discussions F...
• SQL execution error: column "name" of relation "permissions" does not exist
• Failed statement: INSERT INTO public.permissions (name, description, category, resource, action) VALUES ('training.mod...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.centralized_logs ADD CONSTRAINT IF NOT EXISTS chk_centralized_logs_level CHECK (l...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.apm_metrics ADD CONSTRAINT IF NOT EXISTS chk_apm_metrics_type CHECK (metric_type ...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.dr_objectives ADD CONSTRAINT IF NOT EXISTS chk_dr_objectives_priority CHECK (prio...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.dr_test_results ADD CONSTRAINT IF NOT EXISTS chk_dr_test_results_status CHECK (st...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.dr_events ADD CONSTRAINT IF NOT EXISTS chk_dr_events_severity CHECK (severity IN ...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.dr_backup_metadata ADD CONSTRAINT IF NOT EXISTS chk_dr_backup_metadata_status CHE...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.configuration_drift ADD CONSTRAINT IF NOT EXISTS chk_configuration_drift_severity...
• SQL execution error: syntax error at or near "NOT"
• Failed statement: ALTER TABLE public.configuration_compliance_scans ADD CONSTRAINT IF NOT EXISTS chk_configuration_com...

DEPLOYMENT STATUS: SUCCESS
================================================================================