global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true
  
  slack_api_url: '${SLACK_API_URL}'
  
  pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  
  routes:
    # Critical alerts go to PagerDuty and Slack immediately
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      
    # Warning alerts go to Slack
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 30m
      
    # SLA violations get special handling
    - match:
        component: sla
      receiver: 'sla-violations'
      group_wait: 0s
      repeat_interval: 2m
      
    # Business alerts for compliance team
    - match:
        service: compliance
      receiver: 'compliance-team'
      group_wait: 5m
      repeat_interval: 1h
      
    # Database alerts for DBA team
    - match:
        service: database
      receiver: 'database-team'
      group_wait: 2m
      repeat_interval: 15m
      
    # External integration alerts
    - match:
        component: external_data
      receiver: 'integration-team'
      group_wait: 5m
      repeat_interval: 30m

# Inhibition rules to reduce noise
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing for same service
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['service', 'alertname']
    
  # Inhibit individual service alerts if entire cluster is down
  - source_match:
      alertname: 'ClusterDown'
    target_match_re:
      alertname: '.*ServiceDown'
    equal: ['cluster']

# Receiver configurations
receivers:
  - name: 'default'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#alerts-default'
        title: 'RegulensAI Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'critical-alerts'
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_INTEGRATION_KEY}'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        severity: 'critical'
        details:
          firing: '{{ .Firing | len }}'
          resolved: '{{ .Resolved | len }}'
          
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#alerts-critical'
        color: 'danger'
        title: '🚨 CRITICAL ALERT - RegulensAI'
        title_link: 'https://grafana.regulensai.com'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Runbook:* {{ .Annotations.runbook_url }}
          *Service:* {{ .Labels.service }}
          *Component:* {{ .Labels.component }}
          {{ end }}
        fields:
          - title: 'Environment'
            value: 'Production'
            short: true
          - title: 'Firing Alerts'
            value: '{{ .Firing | len }}'
            short: true
            
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 CRITICAL: RegulensAI Alert - {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: |
          Critical alert fired in RegulensAI production environment.
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Component: {{ .Labels.component }}
          Runbook: {{ .Annotations.runbook_url }}
          
          {{ end }}
          
          Please investigate immediately.
          
  - name: 'warning-alerts'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#alerts-warning'
        color: 'warning'
        title: '⚠️ WARNING - RegulensAI'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.service }}
          {{ end }}
          
  - name: 'sla-violations'
    pagerduty_configs:
      - routing_key: '${PAGERDUTY_SLA_KEY}'
        description: 'SLA Violation: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        severity: 'critical'
        
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#sla-violations'
        color: 'danger'
        title: '📊 SLA VIOLATION - RegulensAI'
        text: |
          {{ range .Alerts }}
          *SLA Violation:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.service }}
          *Impact:* Customer-facing service degradation
          {{ end }}
          
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '📊 SLA VIOLATION: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: |
          SLA violation detected in RegulensAI production environment.
          
          This requires immediate attention as it impacts customer experience.
          
          {{ range .Alerts }}
          Violation: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          
  - name: 'compliance-team'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#compliance-alerts'
        color: 'warning'
        title: '⚖️ COMPLIANCE ALERT - RegulensAI'
        text: |
          {{ range .Alerts }}
          *Compliance Issue:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Tenant:* {{ .Labels.tenant_id }}
          {{ end }}
          
    email_configs:
      - to: '<EMAIL>'
        subject: '⚖️ Compliance Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: |
          Compliance-related alert in RegulensAI.
          
          {{ range .Alerts }}
          Issue: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Tenant: {{ .Labels.tenant_id }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
          
  - name: 'database-team'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#database-alerts'
        color: 'warning'
        title: '🗄️ DATABASE ALERT - RegulensAI'
        text: |
          {{ range .Alerts }}
          *Database Issue:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Component:* {{ .Labels.component }}
          {{ end }}
          
    email_configs:
      - to: '<EMAIL>'
        subject: '🗄️ Database Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'integration-team'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#integration-alerts'
        color: 'warning'
        title: '🔗 INTEGRATION ALERT - RegulensAI'
        text: |
          {{ range .Alerts }}
          *Integration Issue:* {{ .Annotations.summary }}
          *Provider:* {{ .Labels.provider }}
          *Description:* {{ .Annotations.description }}
          {{ end }}
          
    email_configs:
      - to: '<EMAIL>'
        subject: '🔗 Integration Alert: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

# Webhook configurations for custom integrations
webhook_configs:
  - name: 'custom-webhook'
    webhook_configs:
      - url: 'https://hooks.regulensai.com/alerts'
        send_resolved: true
        http_config:
          bearer_token: '${WEBHOOK_TOKEN}'
        max_alerts: 10
