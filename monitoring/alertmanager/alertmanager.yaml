global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'smtp_password_from_env'
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

# Templates for alert formatting
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alert routing
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    # Critical alerts - immediate escalation
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      repeat_interval: 15m
      routes:
        # Business critical alerts go to executives
        - match:
            escalation_level: business_critical
          receiver: 'executive-team'
          repeat_interval: 30m
        # Security incidents
        - match:
            escalation_level: security_immediate
          receiver: 'security-team'
          repeat_interval: 10m
        # Infrastructure critical
        - match:
            escalation_level: immediate
          receiver: 'devops-oncall'
          repeat_interval: 15m

    # Warning alerts - standard escalation
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 2h
      routes:
        # Business warnings
        - match:
            escalation_level: business_standard
          receiver: 'business-team'
          repeat_interval: 4h
        # Proactive alerts
        - match:
            escalation_level: proactive
          receiver: 'proactive-monitoring'
          repeat_interval: 6h

    # Info alerts - notification only
    - match:
        severity: info
      receiver: 'info-notifications'
      group_wait: 5m
      repeat_interval: 24h

# Alert receivers and notification channels
receivers:
  - name: 'default'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#regulensai-alerts'
        title: 'RegulensAI Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] RegulensAI Alert: {{ .GroupLabels.alertname }}'
        body: |
          Alert: {{ .GroupLabels.alertname }}
          Severity: {{ .CommonLabels.severity }}
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          Action Required: {{ .Annotations.action_required }}
          
          Labels:
          {{ range .Labels.SortedPairs }}  {{ .Name }}: {{ .Value }}
          {{ end }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#regulensai-critical'
        color: 'danger'
        title: '🚨 CRITICAL ALERT: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **Summary:** {{ .Annotations.summary }}
          **Description:** {{ .Annotations.description }}
          **Action Required:** {{ .Annotations.action_required }}
          **Runbook:** {{ .Annotations.runbook_url }}
          {{ end }}
    webhook_configs:
      - url: 'https://api.regulens.ai/webhooks/alerts/critical'
        send_resolved: true

  - name: 'executive-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[BUSINESS CRITICAL] RegulensAI Alert: {{ .GroupLabels.alertname }}'
        body: |
          BUSINESS CRITICAL ALERT
          
          Alert: {{ .GroupLabels.alertname }}
          Impact: Business operations affected
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Business Impact: {{ .Annotations.business_impact }}
          Immediate Action Required: {{ .Annotations.action_required }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#executive-alerts'
        color: 'danger'
        title: '🔥 BUSINESS CRITICAL: {{ .GroupLabels.alertname }}'
        text: |
          @channel - Business critical alert requiring immediate attention
          {{ range .Alerts }}
          **Summary:** {{ .Annotations.summary }}
          **Business Impact:** {{ .Annotations.business_impact }}
          **Action Required:** {{ .Annotations.action_required }}
          {{ end }}

  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[SECURITY INCIDENT] RegulensAI Alert: {{ .GroupLabels.alertname }}'
        body: |
          SECURITY INCIDENT DETECTED
          
          Alert: {{ .GroupLabels.alertname }}
          Severity: {{ .CommonLabels.severity }}
          
          {{ range .Alerts }}
          Summary: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Security Impact: {{ .Annotations.security_impact }}
          Immediate Action: {{ .Annotations.action_required }}
          Incident Response: {{ .Annotations.runbook_url }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-incidents'
        color: 'danger'
        title: '🛡️ SECURITY INCIDENT: {{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'https://api.regulens.ai/webhooks/security/incident'
        send_resolved: true

  - name: 'devops-oncall'
    email_configs:
      - to: '<EMAIL>'
        subject: '[URGENT] RegulensAI Infrastructure Alert: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#devops-oncall'
        color: 'warning'
        title: '⚠️ Infrastructure Alert: {{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'https://api.regulens.ai/webhooks/alerts/infrastructure'

  - name: 'warning-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#regulensai-warnings'
        color: 'warning'
        title: '⚠️ Warning: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **Summary:** {{ .Annotations.summary }}
          **Description:** {{ .Annotations.description }}
          {{ if .Annotations.action_required }}**Action:** {{ .Annotations.action_required }}{{ end }}
          {{ end }}

  - name: 'business-team'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[BUSINESS ALERT] RegulensAI: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#business-alerts'
        color: 'warning'
        title: '📊 Business Alert: {{ .GroupLabels.alertname }}'

  - name: 'proactive-monitoring'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#proactive-monitoring'
        color: 'good'
        title: '🔮 Predictive Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **Prediction:** {{ .Annotations.summary }}
          **Trend Analysis:** {{ .Annotations.description }}
          **Proactive Action:** {{ .Annotations.action_required }}
          {{ end }}

  - name: 'info-notifications'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#regulensai-info'
        color: 'good'
        title: 'ℹ️ Info: {{ .GroupLabels.alertname }}'

# Inhibition rules to reduce alert noise
inhibit_rules:
  # Inhibit warning alerts when critical alerts are firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhibit info alerts when warning or critical alerts are firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance']

  # Inhibit database alerts when service is down
  - source_match:
      alertname: 'RegulensAIServiceDown'
    target_match:
      category: 'database'
    equal: ['instance']

  # Inhibit performance alerts when infrastructure alerts are firing
  - source_match:
      category: 'infrastructure'
    target_match:
      category: 'performance'
    equal: ['instance']

# Silence configuration for maintenance windows
silences:
  # Example: Maintenance window silencing
  # This would typically be managed through the API or UI
  - matchers:
      - name: alertname
        value: MaintenanceWindow
    startsAt: "2024-01-29T02:00:00Z"
    endsAt: "2024-01-29T04:00:00Z"
    createdBy: "<EMAIL>"
    comment: "Scheduled maintenance window"
