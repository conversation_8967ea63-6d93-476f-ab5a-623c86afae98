{"dashboard": {"id": null, "title": "RegulensAI Technical Operations", "tags": ["regulensai", "technical", "infrastructure"], "style": "dark", "timezone": "browser", "refresh": "15s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "{{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "100 * (1 - ((node_memory_MemAvailable_bytes) / (node_memory_MemTotal_bytes)))", "legendFormat": "{{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Database Connections", "type": "timeseries", "targets": [{"expr": "pg_stat_database_numbackends{datname=\"regulensai\"}", "legendFormat": "Active Connections"}, {"expr": "pg_settings_max_connections", "legendFormat": "Max Connections"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}}, {"id": 4, "title": "Database Query Performance", "type": "timeseries", "targets": [{"expr": "rate(pg_stat_database_tup_fetched{datname=\"regulensai\"}[5m])", "legendFormat": "Rows Fetched/sec"}, {"expr": "rate(pg_stat_database_tup_inserted{datname=\"regulensai\"}[5m])", "legendFormat": "Rows Inserted/sec"}, {"expr": "rate(pg_stat_database_tup_updated{datname=\"regulensai\"}[5m])", "legendFormat": "Rows Updated/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ops"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}}, {"id": 5, "title": "Redis Performance", "type": "timeseries", "targets": [{"expr": "rate(redis_commands_processed_total[5m])", "legendFormat": "Commands/sec"}, {"expr": "redis_connected_clients", "legendFormat": "Connected Clients"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}}, {"id": 6, "title": "API Request Rate", "type": "timeseries", "targets": [{"expr": "sum(rate(http_requests_total{job=\"regulensai-api\"}[5m])) by (method, status)", "legendFormat": "{{method}} {{status}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "API Error Rate", "type": "timeseries", "targets": [{"expr": "sum(rate(http_requests_total{job=\"regulensai-api\", status=~\"5..\"}[5m])) / sum(rate(http_requests_total{job=\"regulensai-api\"}[5m])) * 100", "legendFormat": "Error Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "Kubernetes Pod Status", "type": "table", "targets": [{"expr": "kube_pod_status_phase{namespace=\"regulensai-prod\"}", "legendFormat": "{{pod}} - {{phase}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}}, {"id": 9, "title": "Disk Usage", "type": "gauge", "targets": [{"expr": "100 - ((node_filesystem_avail_bytes{mountpoint=\"/\"} * 100) / node_filesystem_size_bytes{mountpoint=\"/\"})", "legendFormat": "{{instance}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 85}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}}, {"id": 10, "title": "Network I/O", "type": "timeseries", "targets": [{"expr": "rate(node_network_receive_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{instance}} - {{device}} RX"}, {"expr": "rate(node_network_transmit_bytes_total{device!=\"lo\"}[5m])", "legendFormat": "{{instance}} - {{device}} TX"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "Bps"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}}]}}