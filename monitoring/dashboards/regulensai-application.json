{"dashboard": {"id": null, "title": "RegulensAI Application Metrics", "tags": ["regulensai", "application", "business-logic"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-6h", "to": "now"}, "panels": [{"id": 1, "title": "User Authentication Metrics", "type": "timeseries", "targets": [{"expr": "rate(regulensai_user_logins_total[5m])", "legendFormat": "Successful Logins/min"}, {"expr": "rate(regulensai_user_login_failures_total[5m])", "legendFormat": "Failed Logins/min"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "cpm"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Training Portal Usage", "type": "timeseries", "targets": [{"expr": "rate(regulensai_training_module_starts_total[5m])", "legendFormat": "Module Starts/min"}, {"expr": "rate(regulensai_training_module_completions_total[5m])", "legendFormat": "Module Completions/min"}, {"expr": "rate(regulensai_training_assessment_attempts_total[5m])", "legendFormat": "Assessment Attempts/min"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "cpm"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Compliance Task Metrics", "type": "stat", "targets": [{"expr": "sum(regulensai_compliance_tasks_total{status=\"pending\"})", "legendFormat": "Pending Tasks"}, {"expr": "sum(regulensai_compliance_tasks_total{status=\"in_progress\"})", "legendFormat": "In Progress"}, {"expr": "sum(regulensai_compliance_tasks_total{status=\"completed\"})", "legendFormat": "Completed"}, {"expr": "sum(regulensai_compliance_tasks_total{status=\"overdue\"})", "legendFormat": "Overdue"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}}, {"id": 4, "title": "API Endpoint Performance", "type": "table", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"regulensai-api\"}[5m])) by (le, endpoint))", "legendFormat": "{{endpoint}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 2}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}}, {"id": 5, "title": "Database Query Performance by Table", "type": "timeseries", "targets": [{"expr": "rate(regulensai_db_query_duration_seconds_sum{table=\"users\"}[5m]) / rate(regulensai_db_query_duration_seconds_count{table=\"users\"}[5m])", "legendFormat": "Users Table"}, {"expr": "rate(regulensai_db_query_duration_seconds_sum{table=\"compliance_tasks\"}[5m]) / rate(regulensai_db_query_duration_seconds_count{table=\"compliance_tasks\"}[5m])", "legendFormat": "Compliance Tasks"}, {"expr": "rate(regulensai_db_query_duration_seconds_sum{table=\"training_modules\"}[5m]) / rate(regulensai_db_query_duration_seconds_count{table=\"training_modules\"}[5m])", "legendFormat": "Training Modules"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}}, {"id": 6, "title": "Regulatory Document Processing", "type": "timeseries", "targets": [{"expr": "rate(regulensai_document_uploads_total[5m])", "legendFormat": "Document Uploads/min"}, {"expr": "rate(regulensai_document_processing_total[5m])", "legendFormat": "Documents Processed/min"}, {"expr": "rate(regulensai_document_analysis_total[5m])", "legendFormat": "AI Analysis Completed/min"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "cpm"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "AI Service Usage", "type": "timeseries", "targets": [{"expr": "rate(regulensai_openai_requests_total[5m])", "legendFormat": "OpenAI Requests/min"}, {"expr": "rate(regulensai_claude_requests_total[5m])", "legendFormat": "Claude Requests/min"}, {"expr": "rate(regulensai_embedding_requests_total[5m])", "legendFormat": "Embedding Requests/min"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "cpm"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "<PERSON><PERSON> Hit Rates", "type": "gauge", "targets": [{"expr": "rate(regulensai_cache_hits_total[5m]) / (rate(regulensai_cache_hits_total[5m]) + rate(regulensai_cache_misses_total[5m])) * 100", "legendFormat": "<PERSON><PERSON> Hit Rate"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}}, {"id": 9, "title": "Background Job Queue", "type": "timeseries", "targets": [{"expr": "regulensai_job_queue_size", "legendFormat": "<PERSON><PERSON> Size"}, {"expr": "rate(regulensai_jobs_processed_total[5m])", "legendFormat": "Jobs Processed/min"}, {"expr": "rate(regulensai_jobs_failed_total[5m])", "legendFormat": "Jobs Failed/min"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}}, {"id": 10, "title": "User Session Duration", "type": "histogram", "targets": [{"expr": "histogram_quantile(0.50, sum(rate(regulensai_session_duration_seconds_bucket[5m])) by (le))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, sum(rate(regulensai_session_duration_seconds_bucket[5m])) by (le))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}}]}}