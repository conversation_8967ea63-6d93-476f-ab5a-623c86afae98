{"dashboard": {"id": null, "title": "RegulensAI Alerting Overview", "tags": ["regulensai", "alerts", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "10s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Active Alerts by Severity", "type": "stat", "targets": [{"expr": "count by (severity) (ALERTS{alertstate=\"firing\"})", "legendFormat": "{{severity}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}}, {"id": 2, "title": "Alert Resolution Time", "type": "timeseries", "targets": [{"expr": "avg(regulensai_alert_resolution_duration_seconds) by (severity)", "legendFormat": "{{severity}} alerts"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}}, {"id": 3, "title": "<PERSON><PERSON> (24h)", "type": "timeseries", "targets": [{"expr": "increase(prometheus_notifications_total[1h])", "legendFormat": "Notifications Sent"}, {"expr": "increase(alertmanager_alerts_received_total[1h])", "legendFormat": "<PERSON><PERSON><PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}}, {"id": 4, "title": "Critical Alerts - Active", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", severity=\"critical\"}", "legendFormat": "{{alertname}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 1}]}}}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "Warning Alerts - Active", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", severity=\"warning\"}", "legendFormat": "{{alertname}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "yellow", "value": 1}]}}}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "Business Impact Alerts", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", category=\"business\"}", "legendFormat": "{{alertname}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "orange", "value": 1}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 18}}, {"id": 7, "title": "Alert Escalation Status", "type": "piechart", "targets": [{"expr": "sum by (escalation_level) (regulensai_alerts_by_escalation)", "legendFormat": "{{escalation_level}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 26}}, {"id": 8, "title": "Notification Channel Status", "type": "table", "targets": [{"expr": "up{job=\"alertmanager\"}", "legendFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"expr": "regulensai_notification_channel_status", "legendFormat": "{{channel}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 26}}, {"id": 9, "title": "Mean Time to Detection (MTTD)", "type": "stat", "targets": [{"expr": "avg(regulensai_mttd_seconds)", "legendFormat": "MTTD"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 300}, {"color": "red", "value": 900}]}, "unit": "s"}}, "gridPos": {"h": 4, "w": 4, "x": 16, "y": 26}}, {"id": 10, "title": "Mean Time to Resolution (MTTR)", "type": "stat", "targets": [{"expr": "avg(regulensai_mttr_seconds)", "legendFormat": "MTTR"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1800}, {"color": "red", "value": 3600}]}, "unit": "s"}}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 26}}, {"id": 11, "title": "Alert Correlation Map", "type": "nodeGraph", "targets": [{"expr": "regulensai_alert_correlations", "legendFormat": "{{source}} -> {{target}}"}], "gridPos": {"h": 4, "w": 8, "x": 16, "y": 30}}]}}