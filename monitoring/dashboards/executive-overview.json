{"dashboard": {"id": null, "title": "RegulensAI Executive Overview", "tags": ["regulensai", "executive", "business"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "System Health Score", "type": "stat", "targets": [{"expr": "avg(regulensai_system_health_score)", "legendFormat": "Health Score"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Compliance Score", "type": "stat", "targets": [{"expr": "avg(regulensai_compliance_score)", "legendFormat": "Compliance Score"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Active Users (24h)", "type": "stat", "targets": [{"expr": "sum(increase(regulensai_user_logins_total[24h]))", "legendFormat": "Active Users"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "System Uptime", "type": "stat", "targets": [{"expr": "avg(up{job=\"regulensai-api\"}) * 100", "legendFormat": "Uptime"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 99}, {"color": "green", "value": 99.9}]}, "unit": "percent", "decimals": 2}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Compliance Tasks Status", "type": "piechart", "targets": [{"expr": "sum by (status) (regulensai_compliance_tasks_by_status)", "legendFormat": "{{status}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}}, {"id": 6, "title": "Training Completion Rate", "type": "gauge", "targets": [{"expr": "avg(regulensai_training_completion_rate)", "legendFormat": "Completion Rate"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}}, {"id": 7, "title": "API Response Time (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"regulensai-api\"}[5m])) by (le))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 2}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}}, {"id": 8, "title": "Business Metrics Trend", "type": "timeseries", "targets": [{"expr": "regulensai_compliance_score", "legendFormat": "Compliance Score"}, {"expr": "regulensai_training_completion_rate", "legendFormat": "Training Completion"}, {"expr": "regulensai_system_health_score", "legendFormat": "System Health"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}, {"id": 9, "title": "Critical Alerts", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", severity=\"critical\"}", "legendFormat": "{{alertname}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 10, "title": "User Activity Heatmap", "type": "heatmap", "targets": [{"expr": "sum by (hour) (increase(regulensai_user_activity_by_hour[1h]))", "legendFormat": "{{hour}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}]}}