groups:
  - name: regulensai.critical
    rules:
      - alert: RegulensAIServiceDown
        expr: up{job="regulensai-api"} == 0
        for: 1m
        labels:
          severity: critical
          category: infrastructure
          escalation_level: immediate
        annotations:
          summary: "RegulensAI API service is down"
          description: "RegulensAI API service has been down for more than 1 minute. This affects all user operations."
          runbook_url: "https://docs.regulens.ai/runbooks/service-down"
          action_required: "Check pod status, restart service if needed"

      - alert: DatabaseConnectionFailure
        expr: regulensai_db_connections_failed_total > 10
        for: 2m
        labels:
          severity: critical
          category: database
          escalation_level: immediate
        annotations:
          summary: "High database connection failures"
          description: "Database connection failures have exceeded 10 in the last 2 minutes"
          runbook_url: "https://docs.regulens.ai/runbooks/database-issues"
          action_required: "Check database connectivity and pool configuration"

      - alert: ComplianceSLABreach
        expr: regulensai_compliance_tasks_overdue > 5
        for: 5m
        labels:
          severity: critical
          category: business
          escalation_level: business_critical
        annotations:
          summary: "Compliance SLA breach detected"
          description: "More than 5 compliance tasks are overdue, risking regulatory violations"
          runbook_url: "https://docs.regulens.ai/runbooks/compliance-sla"
          action_required: "Review overdue tasks and escalate to compliance team"

      - alert: HighAPIErrorRate
        expr: (sum(rate(http_requests_total{job="regulensai-api", status=~"5.."}[5m])) / sum(rate(http_requests_total{job="regulensai-api"}[5m]))) * 100 > 5
        for: 3m
        labels:
          severity: critical
          category: application
          escalation_level: immediate
        annotations:
          summary: "High API error rate detected"
          description: "API error rate is {{ $value }}% over the last 5 minutes"
          runbook_url: "https://docs.regulens.ai/runbooks/high-error-rate"
          action_required: "Check application logs and recent deployments"

  - name: regulensai.warning
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          category: infrastructure
          escalation_level: standard
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"
          runbook_url: "https://docs.regulens.ai/runbooks/high-cpu"
          action_required: "Monitor and consider scaling if sustained"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          category: infrastructure
          escalation_level: standard
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"
          runbook_url: "https://docs.regulens.ai/runbooks/high-memory"
          action_required: "Check for memory leaks and consider scaling"

      - alert: SlowAPIResponse
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job="regulensai-api"}[5m])) by (le)) > 2
        for: 5m
        labels:
          severity: warning
          category: performance
          escalation_level: standard
        annotations:
          summary: "Slow API response times"
          description: "95th percentile response time is {{ $value }}s"
          runbook_url: "https://docs.regulens.ai/runbooks/slow-response"
          action_required: "Check database performance and optimize queries"

      - alert: TrainingCompletionRateLow
        expr: regulensai_training_completion_rate < 70
        for: 15m
        labels:
          severity: warning
          category: business
          escalation_level: business_standard
        annotations:
          summary: "Low training completion rate"
          description: "Training completion rate is {{ $value }}%, below 70% threshold"
          runbook_url: "https://docs.regulens.ai/runbooks/training-completion"
          action_required: "Review training content and user engagement"

      - alert: DatabaseSlowQueries
        expr: rate(regulensai_db_query_duration_seconds_sum[5m]) / rate(regulensai_db_query_duration_seconds_count[5m]) > 1
        for: 10m
        labels:
          severity: warning
          category: database
          escalation_level: standard
        annotations:
          summary: "Slow database queries detected"
          description: "Average query time is {{ $value }}s"
          runbook_url: "https://docs.regulens.ai/runbooks/slow-queries"
          action_required: "Analyze slow queries and optimize indexes"

  - name: regulensai.info
    rules:
      - alert: NewUserRegistration
        expr: increase(regulensai_user_registrations_total[1h]) > 10
        for: 0m
        labels:
          severity: info
          category: business
          escalation_level: none
        annotations:
          summary: "High user registration activity"
          description: "{{ $value }} new users registered in the last hour"
          action_required: "Monitor for capacity planning"

      - alert: ComplianceTasksDue
        expr: regulensai_compliance_tasks_due_24h > 0
        for: 0m
        labels:
          severity: info
          category: business
          escalation_level: notification
        annotations:
          summary: "Compliance tasks due within 24 hours"
          description: "{{ $value }} compliance tasks are due within 24 hours"
          action_required: "Send reminder notifications to assigned users"

      - alert: BackupCompleted
        expr: regulensai_backup_success == 1
        for: 0m
        labels:
          severity: info
          category: operations
          escalation_level: none
        annotations:
          summary: "Database backup completed successfully"
          description: "Scheduled backup completed at {{ $labels.timestamp }}"
          action_required: "No action required"

  - name: regulensai.predictive
    rules:
      - alert: DiskSpaceProjection
        expr: predict_linear(node_filesystem_avail_bytes{mountpoint="/"}[6h], 24*3600) < 0
        for: 1h
        labels:
          severity: warning
          category: infrastructure
          escalation_level: proactive
        annotations:
          summary: "Disk space will be exhausted in 24 hours"
          description: "Based on current usage trends, disk space will be exhausted"
          runbook_url: "https://docs.regulens.ai/runbooks/disk-space"
          action_required: "Clean up logs and temporary files, plan storage expansion"

      - alert: DatabaseConnectionPoolTrend
        expr: predict_linear(regulensai_db_connections_active[2h], 4*3600) > regulensai_db_connections_max * 0.9
        for: 30m
        labels:
          severity: warning
          category: database
          escalation_level: proactive
        annotations:
          summary: "Database connection pool approaching limits"
          description: "Connection pool usage trending towards maximum capacity"
          runbook_url: "https://docs.regulens.ai/runbooks/connection-pool"
          action_required: "Review connection usage and consider pool size increase"

      - alert: ComplianceScoreTrend
        expr: predict_linear(regulensai_compliance_score[24h], 7*24*3600) < 90
        for: 2h
        labels:
          severity: warning
          category: business
          escalation_level: business_proactive
        annotations:
          summary: "Compliance score trending downward"
          description: "Compliance score projected to fall below 90% in 7 days"
          runbook_url: "https://docs.regulens.ai/runbooks/compliance-trend"
          action_required: "Review compliance processes and address declining metrics"

  - name: regulensai.business_impact
    rules:
      - alert: RegulatoryDeadlineMissed
        expr: regulensai_regulatory_deadlines_missed > 0
        for: 0m
        labels:
          severity: critical
          category: business
          escalation_level: executive
        annotations:
          summary: "Regulatory deadline missed"
          description: "{{ $value }} regulatory deadlines have been missed"
          runbook_url: "https://docs.regulens.ai/runbooks/regulatory-deadline"
          action_required: "Immediate escalation to compliance officer and legal team"

      - alert: TrainingCertificationExpiring
        expr: regulensai_training_certifications_expiring_7d > 10
        for: 0m
        labels:
          severity: warning
          category: business
          escalation_level: business_standard
        annotations:
          summary: "Training certifications expiring soon"
          description: "{{ $value }} training certifications expire within 7 days"
          runbook_url: "https://docs.regulens.ai/runbooks/certification-expiry"
          action_required: "Send renewal notifications and schedule training sessions"

      - alert: AuditTrailGap
        expr: regulensai_audit_log_gaps > 0
        for: 5m
        labels:
          severity: critical
          category: compliance
          escalation_level: immediate
        annotations:
          summary: "Audit trail gap detected"
          description: "Gap in audit logging detected, compliance at risk"
          runbook_url: "https://docs.regulens.ai/runbooks/audit-gap"
          action_required: "Investigate logging system and restore audit trail"

      - alert: SecurityIncidentDetected
        expr: regulensai_security_incidents > 0
        for: 0m
        labels:
          severity: critical
          category: security
          escalation_level: security_immediate
        annotations:
          summary: "Security incident detected"
          description: "{{ $value }} security incidents detected"
          runbook_url: "https://docs.regulens.ai/runbooks/security-incident"
          action_required: "Activate incident response team immediately"
