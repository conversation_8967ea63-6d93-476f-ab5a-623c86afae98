{"name": "regulens-ai-frontend", "version": "1.0.0", "description": "RegulensAI Financial Compliance Platform - Frontend", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.0", "@supabase/supabase-js": "^2.38.0", "axios": "^1.6.0", "typescript": "^4.9.5", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.0", "@mui/x-charts": "^6.18.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-query": "^3.39.0", "jwt-decode": "^4.0.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}