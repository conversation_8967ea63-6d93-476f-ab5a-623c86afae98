{"openapi": "3.1.0", "info": {"title": "Regulens AI - Financial Compliance Platform", "description": "\n# Regulens AI Financial Compliance Platform API\n\n**Enterprise-grade financial compliance platform** for banks, financial institutions, and fintech companies.\n\n## 🚀 Quick Start\n\n### Authentication\nAll endpoints require JWT authentication. First, obtain a token:\n\n```bash\ncurl -X POST \"http://localhost:8000/v1/auth/login\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"username\": \"your_username\", \"password\": \"your_password\"}'\n```\n\nThen include the token in subsequent requests:\n```bash\ncurl -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\\n  \"http://localhost:8000/v1/regulatory/sources\"\n```\n\n### Multi-tenancy\nInclude your tenant ID in all requests:\n```bash\ncurl -H \"X-Tenant-ID: your-tenant-id\" \\\n     -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\\n     \"http://localhost:8000/v1/compliance/tasks\"\n```\n\n## 🏗️ Core Features\n\n### 📊 Regulatory Monitoring\n- Real-time monitoring of SEC, FCA, ECB, and other regulatory sources\n- Automated change detection and impact assessment\n- Customizable alert thresholds and notification workflows\n\n### 🛡️ AML/KYC Compliance\n- Customer risk scoring and profiling\n- Real-time transaction monitoring\n- Sanctions and PEP screening\n- Automated suspicious activity reporting\n\n### 🤖 AI-Powered Insights\n- Regulatory document analysis with GPT-4/Claude\n- Natural language Q&A for compliance questions\n- Automated policy interpretation and summarization\n- Entity extraction from regulatory texts\n\n### 📋 Workflow Management\n- Automated compliance task creation and assignment\n- Escalation workflows with SLA monitoring\n- Audit trail generation and compliance reporting\n- Integration with external GRC systems\n\n### 📈 Analytics & Reporting\n- Risk analytics and performance benchmarking\n- Compliance metrics and KPI dashboards\n- Predictive analytics for regulatory impact\n- Audit-ready compliance reports\n\n## 🔒 Security Features\n\n- **JWT Authentication**: Secure token-based authentication\n- **Multi-tenant Isolation**: Complete data separation by tenant\n- **Rate Limiting**: Configurable request rate limits\n- **Audit Logging**: Complete audit trail for all operations\n- **Role-based Access Control**: Granular permission management\n\n## 📋 API Conventions\n\n### Response Format\nAll API responses follow a consistent format:\n\n```json\n{\n  \"success\": true,\n  \"data\": { ... },\n  \"message\": \"Operation completed successfully\",\n  \"timestamp\": \"2024-01-15T10:30:00Z\",\n  \"request_id\": \"req_*********\"\n}\n```\n\n### Error Handling\nError responses include detailed information:\n\n```json\n{\n  \"success\": false,\n  \"error\": \"VALIDATION_ERROR\",\n  \"message\": \"Invalid input parameters\",\n  \"details\": {\n    \"field\": \"customer_id\",\n    \"issue\": \"Required field missing\"\n  },\n  \"timestamp\": \"2024-01-15T10:30:00Z\",\n  \"request_id\": \"req_*********\"\n}\n```\n\n### Pagination\nList endpoints support pagination:\n\n```json\n{\n  \"data\": [...],\n  \"pagination\": {\n    \"page\": 1,\n    \"per_page\": 20,\n    \"total\": 150,\n    \"total_pages\": 8\n  }\n}\n```\n\n## 📚 Integration Examples\n\n### Python Example\n```python\nimport requests\n\n# Authentication\nauth_response = requests.post(\n    \"http://localhost:8000/v1/auth/login\",\n    json={\"username\": \"user\", \"password\": \"pass\"}\n)\ntoken = auth_response.json()[\"data\"][\"access_token\"]\n\n# Make authenticated request\nheaders = {\n    \"Authorization\": f\"Bearer {token}\",\n    \"X-Tenant-ID\": \"your-tenant-id\"\n}\nresponse = requests.get(\n    \"http://localhost:8000/v1/regulatory/sources\",\n    headers=headers\n)\n```\n\n### JavaScript Example\n```javascript\n// Authentication\nconst authResponse = await fetch('/v1/auth/login', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({ username: 'user', password: 'pass' })\n});\nconst { access_token } = await authResponse.json();\n\n// Make authenticated request\nconst response = await fetch('/v1/regulatory/sources', {\n    headers: {\n        'Authorization': `Bearer ${access_token}`,\n        'X-Tenant-ID': 'your-tenant-id'\n    }\n});\n```\n\n## 🔧 Configuration\n\nThe platform is highly configurable through environment variables:\n\n- **Database**: Supabase PostgreSQL with connection pooling\n- **AI Models**: OpenAI GPT-4, Anthropic Claude integration\n- **Monitoring**: Jaeger tracing, Prometheus metrics\n- **Security**: Configurable JWT, rate limiting, CORS\n\n## 📞 Support\n\n- **Documentation Portal**: http://localhost:8501\n- **Testing Portal**: http://localhost:8502\n- **Health Check**: http://localhost:8000/v1/health\n- **System Metrics**: http://localhost:8000/v1/metrics\n\nFor technical support, please refer to the comprehensive documentation portal.\n", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "Development server"}, {"url": "https://api.regulens-ai.com", "description": "Production server"}], "paths": {"/v1/health": {"get": {"tags": ["System", "Health"], "summary": "Health Check", "description": "Health check endpoint.", "operationId": "health_check_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/auth/login": {"post": {"tags": ["Authentication", "Authentication"], "summary": "<PERSON><PERSON>", "description": "Authenticate user and return JWT tokens.\n\n- **email**: User email address\n- **password**: User password\n- **remember_me**: Extended session duration (optional)", "operationId": "login_v1_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/auth/refresh": {"post": {"tags": ["Authentication", "Authentication"], "summary": "Refresh <PERSON>", "description": "Refresh access token using refresh token.\n\n- **refresh_token**: Valid refresh token", "operationId": "refresh_token_v1_auth_refresh_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/auth/logout": {"post": {"tags": ["Authentication", "Authentication"], "summary": "Logout", "description": "Logout current user.\n\nNote: In a production system, you would typically maintain a token blacklist\nto invalidate tokens before their natural expiration.", "operationId": "logout_v1_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogoutResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/auth/change-password": {"post": {"tags": ["Authentication", "Authentication"], "summary": "Change Password", "description": "Change user password.\n\n- **current_password**: Current password for verification\n- **new_password**: New password\n- **confirm_password**: Confirm new password", "operationId": "change_password_v1_auth_change_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/auth/me": {"get": {"tags": ["Authentication", "Authentication"], "summary": "Get Current User Info", "description": "Get current user information.", "operationId": "get_current_user_info_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Current User Info V1 Auth Me Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/auth/forgot-password": {"post": {"tags": ["Authentication", "Authentication"], "summary": "Forgot Password", "description": "Initiate password reset process.\n\n- **email**: User email address\n\nNote: This is a placeholder implementation. In production, you would:\n1. Generate a secure reset token\n2. Store it with expiration time\n3. Send reset email to user\n4. Implement rate limiting", "operationId": "forgot_password_v1_auth_forgot_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tenants/": {"get": {"tags": ["Tenants", "Tenant Management"], "summary": "Get Tenants", "description": "Get list of tenants with pagination and filtering.\n\nRequires permission: tenants.read", "operationId": "get_tenants_v1_tenants__get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "industry", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by industry", "title": "Industry"}, "description": "Filter by industry"}, {"name": "country", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by country", "title": "Country"}, "description": "Filter by country"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by name or domain", "title": "Search"}, "description": "Search by name or domain"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Tenants", "Tenant Management"], "summary": "Create Tenant", "description": "Create a new tenant.\n\nRequires permission: tenants.create", "operationId": "create_tenant_v1_tenants__post", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tenants/{tenant_id}": {"get": {"tags": ["Tenants", "Tenant Management"], "summary": "Get Tenant", "description": "Get tenant by ID.\n\nRequires permission: tenants.read", "operationId": "get_tenant_v1_tenants__tenant_id__get", "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tenant Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Tenants", "Tenant Management"], "summary": "Update Tenant", "description": "Update tenant information.\n\nRequires permission: tenants.update", "operationId": "update_tenant_v1_tenants__tenant_id__put", "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tenant Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Tenants", "Tenant Management"], "summary": "Delete Tenant", "description": "Delete tenant (soft delete by deactivating).\n\nRequires permission: tenants.delete", "operationId": "delete_tenant_v1_tenants__tenant_id__delete", "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tenant Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tenants/{tenant_id}/settings": {"put": {"tags": ["Tenants", "Tenant Management"], "summary": "Update Tenant Settings", "description": "Update tenant settings.\n\nRequires permission: tenants.manage_settings", "operationId": "update_tenant_settings_v1_tenants__tenant_id__settings_put", "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tenant Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantSettingsUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tenants/{tenant_id}/users": {"get": {"tags": ["Tenants", "Tenant Management"], "summary": "Get Tenant Users", "description": "Get users belonging to a tenant.\n\nRequires permission: tenants.read", "operationId": "get_tenant_users_v1_tenants__tenant_id__users_get", "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Tenant Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Tenant Users V1 Tenants  Tenant Id  Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users/": {"get": {"tags": ["Users", "User Management"], "summary": "Get Users", "description": "Get list of users with pagination and filtering.\n\nRequires permission: users.read", "operationId": "get_users_v1_users__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "role", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by role", "title": "Role"}, "description": "Filter by role"}, {"name": "department", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by department", "title": "Department"}, "description": "Filter by department"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by name or email", "title": "Search"}, "description": "Search by name or email"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Users", "User Management"], "summary": "Create User", "description": "Create a new user.\n\nRequires permission: users.create", "operationId": "create_user_v1_users__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users/{user_id}": {"get": {"tags": ["Users", "User Management"], "summary": "Get User", "description": "Get user by ID.\n\nRequires permission: users.read", "operationId": "get_user_v1_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Users", "User Management"], "summary": "Update User", "description": "Update user information.\n\nRequires permission: users.update", "operationId": "update_user_v1_users__user_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Users", "User Management"], "summary": "Delete User", "description": "Delete user (soft delete by deactivating).\n\nRequires permission: users.delete", "operationId": "delete_user_v1_users__user_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users/{user_id}/permissions": {"put": {"tags": ["Users", "User Management"], "summary": "Update User Permissions", "description": "Update user permissions.\n\nRequires permission: users.manage_permissions", "operationId": "update_user_permissions_v1_users__user_id__permissions_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPermissionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/users/permissions/available": {"get": {"tags": ["Users", "User Management"], "summary": "Get Available Permissions", "description": "Get list of available permissions.\n\nRequires permission: users.read", "operationId": "get_available_permissions_v1_users_permissions_available_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Available Permissions V1 Users Permissions Available Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/sources": {"get": {"tags": ["Regulatory"], "summary": "Get Regulatory Sources", "description": "Get all regulatory sources.", "operationId": "get_regulatory_sources_v1_regulatory_sources_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "object"}, "type": "array", "title": "Response Get Regulatory Sources V1 Regulatory Sources Get"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Regulatory"], "summary": "Create Regulatory Source", "description": "Create a new regulatory source.", "operationId": "create_regulatory_source_v1_regulatory_sources_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegulatorySourceCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Regulatory Source V1 Regulatory Sources Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/regulatory/sources/{source_id}": {"get": {"tags": ["Regulatory"], "summary": "Get Regulatory Source", "description": "Get a specific regulatory source.", "operationId": "get_regulatory_source_v1_regulatory_sources__source_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "source_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Source Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Regulatory Source V1 Regulatory Sources  Source Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/sources/{source_id}/toggle": {"put": {"tags": ["Regulatory"], "summary": "Toggle Source Monitoring", "description": "Enable or disable monitoring for a regulatory source.", "operationId": "toggle_source_monitoring_v1_regulatory_sources__source_id__toggle_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "source_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Source Id"}}, {"name": "enabled", "in": "query", "required": true, "schema": {"type": "boolean", "description": "Enable or disable monitoring", "title": "Enabled"}, "description": "Enable or disable monitoring"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Toggle Source Monitoring V1 Regulatory Sources  Source Id  Toggle Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/documents": {"get": {"tags": ["Regulatory"], "summary": "Get Regulatory Documents", "description": "Get regulatory documents with filtering and pagination.", "operationId": "get_regulatory_documents_v1_regulatory_documents_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Page size", "default": 20, "title": "Size"}, "description": "Page size"}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by document type", "title": "Document Type"}, "description": "Filter by document type"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "jurisdiction", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by jurisdiction", "title": "Juris<PERSON>"}, "description": "Filter by jurisdiction"}, {"name": "impact_level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by impact level", "title": "Impact Level"}, "description": "Filter by impact level"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in title and summary", "title": "Search"}, "description": "Search in title and summary"}, {"name": "publication_date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter from date", "title": "Publication Date From"}, "description": "Filter from date"}, {"name": "publication_date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter to date", "title": "Publication Date To"}, "description": "Filter to date"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Regulatory Documents V1 Regulatory Documents Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/documents/{document_id}": {"get": {"tags": ["Regulatory"], "summary": "Get Regulatory Document", "description": "Get a specific regulatory document with optional analysis.", "operationId": "get_regulatory_document_v1_regulatory_documents__document_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "include_analysis", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Include AI analysis results", "default": true, "title": "Include Analysis"}, "description": "Include AI analysis results"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Regulatory Document V1 Regulatory Documents  Document Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/documents/{document_id}/analyze": {"post": {"tags": ["Regulatory"], "summary": "Analyze Document", "description": "Trigger AI analysis of a regulatory document.", "operationId": "analyze_document_v1_regulatory_documents__document_id__analyze_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Document Id"}}, {"name": "force_reanalysis", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Force re-analysis", "default": false, "title": "Force Reanalysis"}, "description": "Force re-analysis"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Analyze Document V1 Regulatory Documents  Document Id  Analyze Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/documents/similar": {"post": {"tags": ["Regulatory"], "summary": "Find Similar Documents", "description": "Find documents similar to a query text or existing document.", "operationId": "find_similar_documents_v1_regulatory_documents_similar_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SimilarDocumentsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "object"}, "type": "array", "title": "Response Find Similar Documents V1 Regulatory Documents Similar Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/regulatory/deadlines": {"get": {"tags": ["Regulatory"], "summary": "Get Compliance Deadlines", "description": "Get upcoming compliance deadlines.", "operationId": "get_compliance_deadlines_v1_regulatory_deadlines_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days_ahead", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Days to look ahead", "default": 90, "title": "Days Ahead"}, "description": "Days to look ahead"}, {"name": "obligation_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by obligation type", "title": "Obligation Type"}, "description": "Filter by obligation type"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Compliance Deadlines V1 Regulatory Deadlines Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/monitoring/status": {"get": {"tags": ["Regulatory"], "summary": "Get Monitoring Status", "description": "Get regulatory monitoring status.", "operationId": "get_monitoring_status_v1_regulatory_monitoring_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Monitoring Status V1 Regulatory Monitoring Status Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/regulatory/monitoring/start": {"post": {"tags": ["Regulatory"], "summary": "Start Monitoring", "description": "Start regulatory monitoring.", "operationId": "start_monitoring_v1_regulatory_monitoring_start_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Start Monitoring V1 Regulatory Monitoring Start Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/regulatory/monitoring/stop": {"post": {"tags": ["Regulatory"], "summary": "Stop Monitoring", "description": "Stop regulatory monitoring.", "operationId": "stop_monitoring_v1_regulatory_monitoring_stop_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Stop Monitoring V1 Regulatory Monitoring Stop Post"}}}}}, "security": [{"HTTPBearer": []}]}}, "/v1/regulatory/analytics/summary": {"get": {"tags": ["Regulatory"], "summary": "Get Regulatory Analytics", "description": "Get regulatory analytics summary.", "operationId": "get_regulatory_analytics_v1_regulatory_analytics_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Number of days for analytics", "default": 30, "title": "Days"}, "description": "Number of days for analytics"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Regulatory Analytics V1 Regulatory Analytics Summary Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/regulatory/export/documents": {"get": {"tags": ["Regulatory"], "summary": "Export Documents", "description": "Export regulatory documents data.", "operationId": "export_documents_v1_regulatory_export_documents_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "format", "in": "query", "required": false, "schema": {"type": "string", "description": "Export format (json, csv)", "default": "json", "title": "Format"}, "description": "Export format (json, csv)"}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by document type", "title": "Document Type"}, "description": "Filter by document type"}, {"name": "jurisdiction", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by jurisdiction", "title": "Juris<PERSON>"}, "description": "Filter by jurisdiction"}, {"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "Days of data to export", "default": 30, "title": "Days"}, "description": "Days of data to export"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Export Documents V1 Regulatory Export Documents Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/compliance/programs": {"get": {"tags": ["Compliance", "Compliance Management"], "summary": "Get Compliance Programs", "description": "Get list of compliance programs.\n\nRequires permission: compliance.programs.read", "operationId": "get_compliance_programs_v1_compliance_programs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "framework", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by framework", "title": "Framework"}, "description": "Filter by framework"}, {"name": "jurisdiction", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by jurisdiction", "title": "Juris<PERSON>"}, "description": "Filter by jurisdiction"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComplianceProgramResponse"}, "title": "Response Get Compliance Programs V1 Compliance Programs Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Compliance", "Compliance Management"], "summary": "Create Compliance Program", "description": "Create a new compliance program.\n\nRequires permission: compliance.programs.create", "operationId": "create_compliance_program_v1_compliance_programs_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplianceProgramCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplianceProgramResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/compliance/programs/{program_id}": {"get": {"tags": ["Compliance", "Compliance Management"], "summary": "Get Compliance Program", "description": "Get compliance program by ID.\n\nRequires permission: compliance.programs.read", "operationId": "get_compliance_program_v1_compliance_programs__program_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "program_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Program Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplianceProgramResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/aml/customers/screen": {"post": {"tags": ["AML/KYC", "AML/KYC Management"], "summary": "Screen Customer", "description": "Screen customer against sanctions, PEP, and adverse media lists.\n\nRequires permission: aml.customers.screen", "operationId": "screen_customer_v1_aml_customers_screen_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerScreeningRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ScreeningResultResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/aml/customers/{customer_id}/screening-history": {"get": {"tags": ["AML/KYC", "AML/KYC Management"], "summary": "Get Customer Screening History", "description": "Get customer screening history.\n\nRequires permission: aml.customers.read", "operationId": "get_customer_screening_history_v1_aml_customers__customer_id__screening_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Customer Id"}}, {"name": "screening_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by screening type", "title": "Screening Type"}, "description": "Filter by screening type"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ScreeningResultResponse"}, "title": "Response Get Customer Screening History V1 Aml Customers  Customer Id  Screening History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/aml/transactions/suspicious": {"get": {"tags": ["AML/KYC", "AML/KYC Management"], "summary": "Get Suspicious Transactions", "description": "Get suspicious transactions requiring review.\n\nRequires permission: aml.transactions.monitor", "operationId": "get_suspicious_transactions_v1_aml_transactions_suspicious_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by monitoring status", "title": "Status"}, "description": "Filter by monitoring status"}, {"name": "risk_threshold", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Minimum risk score", "default": 50, "title": "Risk Threshold"}, "description": "Minimum risk score"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "description": "Maximum number of results", "default": 100, "title": "Limit"}, "description": "Maximum number of results"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionResponse"}, "title": "Response Get Suspicious Transactions V1 Aml Transactions Suspicious Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/aml/sar": {"post": {"tags": ["AML/KYC", "AML/KYC Management"], "summary": "Create Sar", "description": "Create a Suspicious Activity Report.\n\nRequires permission: aml.sar.create", "operationId": "create_sar_v1_aml_sar_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SARCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SARResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tasks/": {"get": {"tags": ["Tasks", "Task Management"], "summary": "Get Tasks", "description": "Get list of tasks with filtering.\n\nRequires permission: compliance.tasks.read", "operationId": "get_tasks_v1_tasks__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "assigned_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by assigned user", "title": "Assigned To"}, "description": "Filter by assigned user"}, {"name": "task_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by task type", "title": "Task Type"}, "description": "Filter by task type"}, {"name": "priority", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by priority", "title": "Priority"}, "description": "Filter by priority"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "description": "Maximum number of results", "default": 100, "title": "Limit"}, "description": "Maximum number of results"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TaskResponse"}, "title": "Response Get Tasks V1 Tasks  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Tasks", "Task Management"], "summary": "Create Task", "description": "Create a new task.\n\nRequires permission: compliance.tasks.create", "operationId": "create_task_v1_tasks__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/tasks/{task_id}": {"put": {"tags": ["Tasks", "Task Management"], "summary": "Update Task", "description": "Update a task.\n\nRequires permission: compliance.tasks.update", "operationId": "update_task_v1_tasks__task_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Task Id"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/reports/dashboard/metrics": {"get": {"tags": ["Reports", "Reporting & Analytics"], "summary": "Get Dashboard Metrics", "description": "Get dashboard metrics overview.\n\nRequires permission: reports.read", "operationId": "get_dashboard_metrics_v1_reports_dashboard_metrics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardMetrics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/reports/compliance/metrics": {"get": {"tags": ["Reports", "Reporting & Analytics"], "summary": "Get Compliance Metrics", "description": "Get detailed compliance metrics.\n\nRequires permission: reports.read", "operationId": "get_compliance_metrics_v1_reports_compliance_metrics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of days to analyze", "default": 30, "title": "Days"}, "description": "Number of days to analyze"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComplianceMetrics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/reports/transactions/metrics": {"get": {"tags": ["Reports", "Reporting & Analytics"], "summary": "Get Transaction Metrics", "description": "Get transaction analytics metrics.\n\nRequires permission: reports.read", "operationId": "get_transaction_metrics_v1_reports_transactions_metrics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of days to analyze", "default": 30, "title": "Days"}, "description": "Number of days to analyze"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionMetrics"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/reports/generate": {"post": {"tags": ["Reports", "Reporting & Analytics"], "summary": "Generate Report", "description": "Generate a compliance report.\n\nRequires permission: reports.create", "operationId": "generate_report_v1_reports_generate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/reports/alerts/summary": {"get": {"tags": ["Reports", "Reporting & Analytics"], "summary": "Get Alerts Summary", "description": "Get alerts summary for the specified period.\n\nRequires permission: reports.read", "operationId": "get_alerts_summary_v1_reports_alerts_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of days to analyze", "default": 7, "title": "Days"}, "description": "Number of days to analyze"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/ai/analyze/risk": {"post": {"tags": ["AI Insights", "AI Insights"], "summary": "Analyze Risk", "description": "Perform AI-powered risk analysis on an entity.\n\nRequires permission: ai.analysis.trigger", "operationId": "analyze_risk_v1_ai_analyze_risk_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnalysisRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RiskAnalysisResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/ai/insights/compliance": {"get": {"tags": ["AI Insights", "AI Insights"], "summary": "Get Compliance Insights", "description": "Get AI-generated compliance insights.\n\nRequires permission: ai.insights.read", "operationId": "get_compliance_insights_v1_ai_insights_compliance_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of days to analyze", "default": 7, "title": "Days"}, "description": "Number of days to analyze"}, {"name": "severity", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by severity", "title": "Severity"}, "description": "Filter by severity"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ComplianceInsight"}, "title": "Response Get Compliance Insights V1 Ai Insights Compliance Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/ai/predictions/alerts": {"get": {"tags": ["AI Insights", "AI Insights"], "summary": "Get Predictive Alerts", "description": "Get AI-generated predictive alerts.\n\nRequires permission: ai.insights.read", "operationId": "get_predictive_alerts_v1_ai_predictions_alerts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "time_horizon", "in": "query", "required": false, "schema": {"type": "string", "description": "Prediction time horizon", "default": "7d", "title": "Time Horizon"}, "description": "Prediction time horizon"}, {"name": "min_probability", "in": "query", "required": false, "schema": {"type": "number", "description": "Minimum probability threshold", "default": 0.7, "title": "Min Probability"}, "description": "Minimum probability threshold"}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PredictiveAlert"}, "title": "Response Get Predictive Alerts V1 Ai Predictions Alerts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/ai/models/status": {"get": {"tags": ["AI Insights", "AI Insights"], "summary": "Get Model Status", "description": "Get AI model status and performance metrics.\n\nRequires permission: ai.models.manage", "operationId": "get_model_status_v1_ai_models_status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/status": {"get": {"tags": ["Operations"], "summary": "Get System Status", "description": "Get overall system status.", "operationId": "get_system_status_v1_operations_status_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/system/detailed-status": {"get": {"tags": ["Operations"], "summary": "Get Detailed System Status", "description": "Get detailed system status including pods, services, and metrics.", "operationId": "get_detailed_system_status_v1_operations_system_detailed_status_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/database/migrations": {"get": {"tags": ["Operations"], "summary": "Get Migration Status", "description": "Get database migration status.", "operationId": "get_migration_status_v1_operations_database_migrations_get", "parameters": [{"name": "env", "in": "query", "required": false, "schema": {"type": "string", "default": "staging", "title": "Env"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/database/health": {"get": {"tags": ["Operations"], "summary": "Get Database Health", "description": "Get database health information.", "operationId": "get_database_health_v1_operations_database_health_get", "parameters": [{"name": "env", "in": "query", "required": false, "schema": {"type": "string", "default": "staging", "title": "Env"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DatabaseHealthResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/database/migrate": {"post": {"tags": ["Operations"], "summary": "Run Migration", "description": "Run database migration.", "operationId": "run_migration_v1_operations_database_migrate_post", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/database/backup": {"post": {"tags": ["Operations"], "summary": "Create Backup", "description": "Create database backup.", "operationId": "create_backup_v1_operations_database_backup_post", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BackupRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/configuration/validate": {"get": {"tags": ["Operations"], "summary": "Validate System Configuration", "description": "Perform comprehensive configuration validation.", "operationId": "validate_system_configuration_v1_operations_configuration_validate_get", "parameters": [{"name": "include_summary", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Include Summary"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigurationValidationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/configuration/summary": {"get": {"tags": ["Operations"], "summary": "Get Configuration Summary Endpoint", "description": "Get current configuration summary.", "operationId": "get_configuration_summary_endpoint_v1_operations_configuration_summary_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/configuration/reload": {"post": {"tags": ["Operations"], "summary": "Reload Configuration", "description": "Reload configuration from environment variables.", "operationId": "reload_configuration_v1_operations_configuration_reload_post", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/configuration/environment/{environment}/validate": {"get": {"tags": ["Operations"], "summary": "Validate Environment Configuration", "description": "Validate configuration for specific environment.", "operationId": "validate_environment_configuration_v1_operations_configuration_environment__environment__validate_get", "parameters": [{"name": "environment", "in": "path", "required": true, "schema": {"type": "string", "title": "Environment"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/metrics/collect": {"get": {"tags": ["Operations"], "summary": "Collect Metrics", "description": "Trigger metrics collection and return current metrics.", "operationId": "collect_metrics_v1_operations_metrics_collect_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/metrics/prometheus": {"get": {"tags": ["Operations"], "summary": "Get Prometheus Metrics", "description": "Get Prometheus-formatted metrics.", "operationId": "get_prometheus_metrics_v1_operations_metrics_prometheus_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/metrics/business": {"get": {"tags": ["Operations"], "summary": "Get Business Metrics", "description": "Get business-specific metrics summary.", "operationId": "get_business_metrics_v1_operations_metrics_business_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/alerts/active": {"get": {"tags": ["Operations"], "summary": "Get Active Alerts", "description": "Get currently active alerts.", "operationId": "get_active_alerts_v1_operations_alerts_active_get", "parameters": [{"name": "severity", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Severity"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/alerts/{alert_id}/acknowledge": {"post": {"tags": ["Operations"], "summary": "Acknowledge <PERSON>", "description": "Acknowledge an active alert.", "operationId": "acknowledge_alert_v1_operations_alerts__alert_id__acknowledge_post", "parameters": [{"name": "alert_id", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}, {"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/monitoring/status": {"get": {"tags": ["Operations"], "summary": "Get Monitoring Status", "description": "Get monitoring stack status.", "operationId": "get_monitoring_status_v1_operations_monitoring_status_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/monitoring/alerts": {"get": {"tags": ["Operations"], "summary": "Get Alert Rules", "description": "Get configured alert rules.", "operationId": "get_alert_rules_v1_operations_monitoring_alerts_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/operations/monitoring/notifications": {"get": {"tags": ["Operations"], "summary": "Get Notification Channels", "description": "Get configured notification channels.", "operationId": "get_notification_channels_v1_operations_monitoring_notifications_get", "parameters": [{"name": "func", "in": "query", "required": true, "schema": {"title": "Func"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint with platform information.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"text/html": {"schema": {"type": "string"}}}}}}}, "/v1/info": {"get": {"summary": "Get Platform Info", "description": "Get platform information and capabilities.", "operationId": "get_platform_info_v1_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/metrics": {"get": {"summary": "Get Platform Metrics", "description": "Get platform performance metrics.", "operationId": "get_platform_metrics_v1_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AnalysisRequest": {"properties": {"analysis_type": {"type": "string", "title": "Analysis Type", "description": "Type of analysis"}, "entity_type": {"type": "string", "title": "Entity Type", "description": "Entity type (customer, transaction, document)"}, "entity_id": {"type": "string", "title": "Entity Id", "description": "Entity ID to analyze"}, "parameters": {"type": "object", "title": "Parameters", "description": "Analysis parameters", "default": {}}}, "type": "object", "required": ["analysis_type", "entity_type", "entity_id"], "title": "AnalysisRequest", "description": "AI analysis request model."}, "BackupRequest": {"properties": {"environment": {"type": "string", "title": "Environment", "description": "Target environment"}, "type": {"type": "string", "title": "Type", "description": "Backup type", "default": "full"}}, "type": "object", "required": ["environment"], "title": "BackupRequest", "description": "Backup request model."}, "ChangePasswordRequest": {"properties": {"current_password": {"type": "string", "title": "Current Password", "description": "Current password"}, "new_password": {"type": "string", "minLength": 8, "title": "New Password", "description": "New password"}, "confirm_password": {"type": "string", "title": "Confirm Password", "description": "Confirm new password"}}, "type": "object", "required": ["current_password", "new_password", "confirm_password"], "title": "ChangePasswordRequest", "description": "Change password request model."}, "ComplianceInsight": {"properties": {"insight_type": {"type": "string", "title": "Insight Type"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "severity": {"type": "string", "title": "Severity"}, "affected_entities": {"items": {"type": "string"}, "type": "array", "title": "Affected Entities"}, "recommendations": {"items": {"type": "string"}, "type": "array", "title": "Recommendations"}, "confidence": {"type": "number", "title": "Confidence", "description": "AI model confidence level for the analysis result (0.0 to 1.0).", "example": 0.85}, "created_at": {"type": "string", "title": "Created At"}}, "type": "object", "required": ["insight_type", "title", "description", "severity", "affected_entities", "recommendations", "confidence", "created_at"], "title": "ComplianceInsight", "description": "Compliance insight response model."}, "ComplianceMetrics": {"properties": {"kyc_completion_rate": {"type": "number", "title": "Kyc Completion Rate"}, "aml_alert_rate": {"type": "number", "title": "<PERSON><PERSON> Alert <PERSON>"}, "sar_filing_rate": {"type": "number", "title": "Sar <PERSON> Rate"}, "risk_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Risk Distribution"}, "regulatory_violations": {"type": "integer", "title": "Regulatory Violations"}, "overdue_reviews": {"type": "integer", "title": "Overdue Reviews"}}, "type": "object", "required": ["kyc_completion_rate", "aml_alert_rate", "sar_filing_rate", "risk_distribution", "regulatory_violations", "overdue_reviews"], "title": "ComplianceMetrics", "description": "Compliance metrics response model."}, "ComplianceProgramCreate": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 2, "title": "Name", "description": "Program name"}, "description": {"type": "string", "minLength": 10, "title": "Description", "description": "Program description"}, "framework": {"type": "string", "title": "Framework", "description": "Regulatory framework (e.g., SOX, GDPR, Basel III)"}, "jurisdiction": {"type": "string", "title": "Juris<PERSON>", "description": "Regulatory jurisdiction"}, "effective_date": {"type": "string", "format": "date-time", "title": "Effective Date", "description": "Program effective date"}, "review_frequency": {"type": "integer", "maximum": 365.0, "minimum": 1.0, "title": "Review Frequency", "description": "Review frequency in days"}, "owner_id": {"type": "string", "title": "Owner Id", "description": "Program owner user ID"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Program active status", "default": true}}, "type": "object", "required": ["name", "description", "framework", "jurisdiction", "effective_date", "review_frequency", "owner_id"], "title": "ComplianceProgramCreate", "description": "Compliance program creation request model."}, "ComplianceProgramResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "framework": {"type": "string", "title": "Framework"}, "jurisdiction": {"type": "string", "title": "Juris<PERSON>"}, "effective_date": {"type": "string", "title": "Effective Date"}, "review_frequency": {"type": "integer", "title": "Review Frequency"}, "owner_id": {"type": "string", "title": "Owner Id"}, "owner_name": {"type": "string", "title": "Owner Name"}, "is_active": {"type": "boolean", "title": "Is Active"}, "requirement_count": {"type": "integer", "title": "Requirement Count"}, "completion_rate": {"type": "number", "title": "Completion Rate"}, "last_review_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Review Date"}, "next_review_date": {"type": "string", "title": "Next Review Date"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "name", "description", "framework", "jurisdiction", "effective_date", "review_frequency", "owner_id", "owner_name", "is_active", "requirement_count", "completion_rate", "last_review_date", "next_review_date", "created_at", "updated_at"], "title": "ComplianceProgramResponse", "description": "Compliance program response model."}, "ConfigurationValidationResponse": {"properties": {"overall_status": {"type": "string", "title": "Overall Status", "description": "Overall validation status"}, "timestamp": {"type": "string", "title": "Timestamp", "description": "Validation timestamp"}, "environment": {"type": "string", "title": "Environment", "description": "Environment being validated"}, "validations": {"type": "object", "title": "Validations", "description": "Detailed validation results"}, "summary": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Summary", "description": "Configuration summary"}}, "type": "object", "required": ["overall_status", "timestamp", "environment", "validations"], "title": "ConfigurationValidationResponse", "description": "Configuration validation response model."}, "CustomerScreeningRequest": {"properties": {"customer_id": {"type": "string", "title": "Customer Id", "description": "Unique customer identifier. Must be alphanumeric, 3-50 characters.", "example": "CUST_001_2024"}, "screening_type": {"type": "string", "title": "Screening Type", "description": "Type of screening (sanctions, pep, adverse_media)"}, "force_refresh": {"type": "boolean", "title": "Force Refresh", "description": "Force refresh of screening data", "default": false}}, "type": "object", "required": ["customer_id", "screening_type"], "title": "CustomerScreeningRequest", "description": "Customer screening request model."}, "DashboardMetrics": {"properties": {"total_customers": {"type": "integer", "title": "Total Customers"}, "high_risk_customers": {"type": "integer", "title": "High Risk Customers"}, "pending_kyc": {"type": "integer", "title": "Pending Kyc"}, "active_alerts": {"type": "integer", "title": "Active Alerts"}, "open_sars": {"type": "integer", "title": "Open Sars"}, "transactions_today": {"type": "integer", "title": "Transactions Today"}, "suspicious_transactions": {"type": "integer", "title": "Suspicious Transactions"}, "compliance_score": {"type": "number", "title": "Compliance Score"}}, "type": "object", "required": ["total_customers", "high_risk_customers", "pending_kyc", "active_alerts", "open_sars", "transactions_today", "suspicious_transactions", "compliance_score"], "title": "DashboardMetrics", "description": "Dashboard metrics response model."}, "DatabaseHealthResponse": {"properties": {"status": {"type": "string", "title": "Status", "description": "Database health status"}, "connections": {"type": "integer", "title": "Connections", "description": "Active connections", "default": 0}, "size": {"type": "string", "title": "Size", "description": "Database size", "default": "0 MB"}, "lastBackup": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lastbackup", "description": "Last backup timestamp"}, "performance": {"type": "object", "title": "Performance", "description": "Performance metrics", "default": {}}}, "type": "object", "required": ["status"], "title": "DatabaseHealthResponse", "description": "Database health response model."}, "ForgotPasswordRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}}, "type": "object", "required": ["email"], "title": "ForgotPasswordRequest", "description": "Forgot password request model."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LoginRequest": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "User password"}, "remember_me": {"type": "boolean", "title": "Remember Me", "description": "Extended session duration", "default": false}}, "type": "object", "required": ["email", "password"], "title": "LoginRequest", "description": "Login request model."}, "LoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "JWT access token"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "JWT refresh token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Token type", "default": "bearer"}, "expires_in": {"type": "integer", "title": "Expires In", "description": "Token expiration time in seconds"}, "user": {"type": "object", "title": "User", "description": "User information"}}, "type": "object", "required": ["access_token", "refresh_token", "expires_in", "user"], "title": "LoginResponse", "description": "Login response model."}, "LogoutResponse": {"properties": {"message": {"type": "string", "title": "Message", "description": "Logout confirmation message"}}, "type": "object", "required": ["message"], "title": "LogoutResponse", "description": "Logout response model."}, "MigrationRequest": {"properties": {"environment": {"type": "string", "title": "Environment", "description": "Target environment"}, "migration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Migration", "description": "Specific migration to run"}, "dryRun": {"type": "boolean", "title": "<PERSON>run", "description": "Dry run mode", "default": false}}, "type": "object", "required": ["environment"], "title": "MigrationRequest", "description": "Migration request model."}, "MigrationStatusResponse": {"properties": {"applied": {"items": {"type": "object"}, "type": "array", "title": "Applied", "description": "Applied migrations", "default": []}, "pending": {"items": {"type": "string"}, "type": "array", "title": "Pending", "description": "Pending migrations", "default": []}, "available": {"items": {"type": "string"}, "type": "array", "title": "Available", "description": "Available migrations", "default": []}, "upToDate": {"type": "boolean", "title": "Uptodate", "description": "Whether schema is up to date", "default": true}}, "type": "object", "title": "MigrationStatusResponse", "description": "Database migration status response."}, "PredictiveAlert": {"properties": {"alert_id": {"type": "string", "title": "<PERSON><PERSON>"}, "prediction_type": {"type": "string", "title": "Prediction Type"}, "entity_type": {"type": "string", "title": "Entity Type"}, "entity_id": {"type": "string", "title": "Entity Id"}, "predicted_risk": {"type": "number", "title": "Predicted Risk"}, "probability": {"type": "number", "title": "Probability"}, "time_horizon": {"type": "string", "title": "Time Horizon"}, "factors": {"items": {"type": "string"}, "type": "array", "title": "Factors"}, "recommended_actions": {"items": {"type": "string"}, "type": "array", "title": "Recommended Actions"}, "created_at": {"type": "string", "title": "Created At"}}, "type": "object", "required": ["alert_id", "prediction_type", "entity_type", "entity_id", "predicted_risk", "probability", "time_horizon", "factors", "recommended_actions", "created_at"], "title": "Predictive<PERSON><PERSON><PERSON>", "description": "Predictive alert response model."}, "RefreshTokenRequest": {"properties": {"refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "Valid refresh token"}}, "type": "object", "required": ["refresh_token"], "title": "RefreshTokenRequest", "description": "Refresh token request model."}, "RegulatorySourceCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the regulatory source"}, "type": {"type": "string", "title": "Type", "description": "Type of source (rss, api, web_scrape)"}, "country_code": {"type": "string", "title": "Country Code", "description": "Country code (US, UK, EU, etc.)"}, "jurisdiction": {"type": "string", "title": "Juris<PERSON>", "description": "Jurisdiction (Federal, State, etc.)"}, "website_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Website Url", "description": "Main website URL"}, "rss_feed_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rss Feed Url", "description": "RSS feed URL"}, "api_endpoint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Endpoint", "description": "API endpoint URL"}, "monitoring_enabled": {"type": "boolean", "title": "Monitoring Enabled", "description": "Enable monitoring", "default": true}}, "type": "object", "required": ["name", "type", "country_code", "jurisdiction"], "title": "RegulatorySourceCreate", "description": "Model for creating a regulatory source."}, "ReportRequest": {"properties": {"report_type": {"type": "string", "title": "Report Type", "description": "Type of report"}, "start_date": {"type": "string", "format": "date-time", "title": "Start Date", "description": "Report start date"}, "end_date": {"type": "string", "format": "date-time", "title": "End Date", "description": "Report end date"}, "filters": {"type": "object", "title": "Filters", "description": "Additional filters", "default": {}}, "format": {"type": "string", "title": "Format", "description": "Report format (json, csv, pdf)", "default": "json"}}, "type": "object", "required": ["report_type", "start_date", "end_date"], "title": "ReportRequest", "description": "Report generation request model."}, "RiskAnalysisResponse": {"properties": {"entity_id": {"type": "string", "title": "Entity Id"}, "entity_type": {"type": "string", "title": "Entity Type"}, "risk_score": {"type": "number", "title": "Risk Score", "description": "Calculated risk score between 0.0 (lowest risk) and 1.0 (highest risk).", "example": 0.25}, "risk_factors": {"items": {"type": "object"}, "type": "array", "title": "Risk Factors"}, "recommendations": {"items": {"type": "string"}, "type": "array", "title": "Recommendations"}, "confidence_level": {"type": "number", "title": "Confidence Level"}, "analysis_date": {"type": "string", "title": "Analysis Date"}}, "type": "object", "required": ["entity_id", "entity_type", "risk_score", "risk_factors", "recommendations", "confidence_level", "analysis_date"], "title": "RiskAnalysisResponse", "description": "Risk analysis response model."}, "SARCreateRequest": {"properties": {"customer_id": {"type": "string", "title": "Customer Id", "description": "Unique customer identifier. Must be alphanumeric, 3-50 characters.", "example": "CUST_001_2024"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "Unique transaction identifier. System generates if not provided.", "example": "TXN_WR_20240115_001"}, "report_type": {"type": "string", "title": "Report Type", "description": "Type of SAR report"}, "suspicious_activity_description": {"type": "string", "minLength": 50, "title": "Suspicious Activity Description", "description": "Description of suspicious activity"}, "amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Amount", "description": "Amount involved"}, "filing_reason": {"type": "string", "title": "Filing Reason", "description": "Reason for filing SAR"}}, "type": "object", "required": ["customer_id", "report_type", "suspicious_activity_description", "filing_reason"], "title": "SARCreateRequest", "description": "SAR creation request model."}, "SARResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "customer_id": {"type": "string", "title": "Customer Id", "description": "Unique customer identifier. Must be alphanumeric, 3-50 characters.", "example": "CUST_001_2024"}, "customer_name": {"type": "string", "title": "Customer Name"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "Unique transaction identifier. System generates if not provided.", "example": "TXN_WR_20240115_001"}, "report_type": {"type": "string", "title": "Report Type"}, "suspicious_activity_description": {"type": "string", "title": "Suspicious Activity Description"}, "amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Amount"}, "status": {"type": "string", "title": "Status"}, "filing_reason": {"type": "string", "title": "Filing Reason"}, "filed_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filed Date"}, "created_at": {"type": "string", "title": "Created At"}}, "type": "object", "required": ["id", "customer_id", "customer_name", "transaction_id", "report_type", "suspicious_activity_description", "amount", "status", "filing_reason", "filed_date", "created_at"], "title": "SARResponse", "description": "SAR response model."}, "ScreeningResultResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "customer_id": {"type": "string", "title": "Customer Id", "description": "Unique customer identifier. Must be alphanumeric, 3-50 characters.", "example": "CUST_001_2024"}, "customer_name": {"type": "string", "title": "Customer Name"}, "screening_type": {"type": "string", "title": "Screening Type"}, "status": {"type": "string", "title": "Status"}, "match_found": {"type": "boolean", "title": "Match Found"}, "match_details": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Match Details"}, "risk_level": {"type": "string", "title": "Risk Level"}, "screened_at": {"type": "string", "title": "Screened At"}}, "type": "object", "required": ["id", "customer_id", "customer_name", "screening_type", "status", "match_found", "match_details", "risk_level", "screened_at"], "title": "ScreeningResultResponse", "description": "Screening result response model."}, "SimilarDocumentsRequest": {"properties": {"query_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query Text", "description": "Text to find similar documents for"}, "document_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Id", "description": "Document ID to find similar documents for"}, "limit": {"type": "integer", "maximum": 50.0, "minimum": 1.0, "title": "Limit", "description": "Maximum number of results", "default": 10}, "threshold": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Similarity threshold", "default": 0.7}}, "type": "object", "title": "SimilarDocumentsRequest", "description": "Model for finding similar documents."}, "SystemStatusResponse": {"properties": {"overall": {"type": "string", "title": "Overall", "description": "Overall system status"}, "database": {"type": "string", "title": "Database", "description": "Database status"}, "api": {"type": "string", "title": "Api", "description": "API service status"}, "monitoring": {"type": "string", "title": "Monitoring", "description": "Monitoring stack status"}, "redis": {"type": "string", "title": "Redis", "description": "Redis status", "default": "unknown"}, "lastCheck": {"type": "string", "title": "Lastcheck", "description": "Last status check timestamp"}}, "type": "object", "required": ["overall", "database", "api", "monitoring", "<PERSON><PERSON><PERSON><PERSON>"], "title": "SystemStatusResponse", "description": "System status response model."}, "TaskCreate": {"properties": {"title": {"type": "string", "maxLength": 200, "minLength": 2, "title": "Title", "description": "Task title"}, "description": {"type": "string", "minLength": 10, "title": "Description", "description": "Task description"}, "task_type": {"type": "string", "title": "Task Type", "description": "Type of task"}, "priority": {"type": "string", "title": "Priority", "description": "Priority level (low, medium, high, critical)"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date", "description": "Due date"}, "assigned_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To", "description": "Assigned user ID"}, "related_entity_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Entity Type", "description": "Related entity type"}, "related_entity_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Entity Id", "description": "Related entity ID"}}, "type": "object", "required": ["title", "description", "task_type", "priority"], "title": "TaskCreate", "description": "Task creation request model."}, "TaskResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "task_type": {"type": "string", "title": "Task Type"}, "status": {"type": "string", "title": "Status"}, "priority": {"type": "string", "title": "Priority"}, "due_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Due Date"}, "assigned_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To"}, "assigned_to_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To Name"}, "created_by": {"type": "string", "title": "Created By"}, "created_by_name": {"type": "string", "title": "Created By Name"}, "related_entity_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Entity Type"}, "related_entity_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Entity Id"}, "completion_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completion Notes"}, "completed_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "title", "description", "task_type", "status", "priority", "due_date", "assigned_to", "assigned_to_name", "created_by", "created_by_name", "related_entity_type", "related_entity_id", "completion_notes", "completed_at", "created_at", "updated_at"], "title": "TaskResponse", "description": "Task response model."}, "TaskUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 2}, {"type": "null"}], "title": "Title", "description": "Task title"}, "description": {"anyOf": [{"type": "string", "minLength": 10}, {"type": "null"}], "title": "Description", "description": "Task description"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status", "description": "Task status"}, "priority": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Priority", "description": "Priority level"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date", "description": "Due date"}, "assigned_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To", "description": "Assigned user ID"}, "completion_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completion Notes", "description": "Completion notes"}}, "type": "object", "title": "TaskUpdate", "description": "Task update request model."}, "TenantCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Name", "description": "Organization name"}, "domain": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Domain", "description": "Organization domain"}, "industry": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Industry", "description": "Industry sector"}, "country": {"type": "string", "maxLength": 2, "minLength": 2, "title": "Country", "description": "Country code (ISO 3166-1 alpha-2)"}, "timezone": {"type": "string", "title": "Timezone", "description": "Default timezone", "default": "UTC"}, "settings": {"type": "object", "title": "Settings", "description": "Tenant-specific settings", "default": {}}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Tenant active status", "default": true}}, "type": "object", "required": ["name", "domain", "country"], "title": "TenantCreate", "description": "Tenant creation request model."}, "TenantListResponse": {"properties": {"tenants": {"items": {"$ref": "#/components/schemas/TenantResponse"}, "type": "array", "title": "Tenants"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "size": {"type": "integer", "title": "Size"}, "pages": {"type": "integer", "title": "Pages"}}, "type": "object", "required": ["tenants", "total", "page", "size", "pages"], "title": "TenantListResponse", "description": "Tenant list response model."}, "TenantResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "domain": {"type": "string", "title": "Domain"}, "industry": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Industry"}, "country": {"type": "string", "title": "Country"}, "timezone": {"type": "string", "title": "Timezone"}, "settings": {"type": "object", "title": "Settings"}, "is_active": {"type": "boolean", "title": "Is Active"}, "user_count": {"type": "integer", "title": "User Count"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "name", "domain", "industry", "country", "timezone", "settings", "is_active", "user_count", "created_at", "updated_at"], "title": "TenantResponse", "description": "Tenant response model."}, "TenantSettingsUpdate": {"properties": {"settings": {"type": "object", "title": "Settings", "description": "Tenant-specific settings"}}, "type": "object", "required": ["settings"], "title": "TenantSettingsUpdate", "description": "Tenant settings update request model."}, "TenantUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 2}, {"type": "null"}], "title": "Name", "description": "Organization name"}, "domain": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 2}, {"type": "null"}], "title": "Domain", "description": "Organization domain"}, "industry": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Industry", "description": "Industry sector"}, "country": {"anyOf": [{"type": "string", "maxLength": 2, "minLength": 2}, {"type": "null"}], "title": "Country", "description": "Country code"}, "timezone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timezone", "description": "Default timezone"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Tenant active status"}}, "type": "object", "title": "TenantUpdate", "description": "Tenant update request model."}, "TransactionMetrics": {"properties": {"total_volume": {"type": "number", "title": "Total Volume"}, "total_count": {"type": "integer", "title": "Total Count"}, "average_amount": {"type": "number", "title": "Average Amount"}, "high_risk_transactions": {"type": "integer", "title": "High Risk Transactions"}, "cross_border_percentage": {"type": "number", "title": "Cross Border Percentage"}, "top_countries": {"items": {"type": "object"}, "type": "array", "title": "Top Countries"}}, "type": "object", "required": ["total_volume", "total_count", "average_amount", "high_risk_transactions", "cross_border_percentage", "top_countries"], "title": "TransactionMetrics", "description": "Transaction metrics response model."}, "TransactionResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "customer_id": {"type": "string", "title": "Customer Id", "description": "Unique customer identifier. Must be alphanumeric, 3-50 characters.", "example": "CUST_001_2024"}, "customer_name": {"type": "string", "title": "Customer Name"}, "transaction_type": {"type": "string", "title": "Transaction Type"}, "amount": {"type": "number", "title": "Amount"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "source_country": {"type": "string", "title": "Source Country"}, "destination_country": {"type": "string", "title": "Destination Country"}, "monitoring_status": {"type": "string", "title": "Monitoring Status"}, "risk_score": {"type": "integer", "title": "Risk Score", "description": "Calculated risk score between 0.0 (lowest risk) and 1.0 (highest risk).", "example": 0.25}, "suspicious_indicators": {"items": {"type": "string"}, "type": "array", "title": "Suspicious Indicators"}, "requires_sar": {"type": "boolean", "title": "Requires Sa<PERSON>"}, "created_at": {"type": "string", "title": "Created At"}}, "type": "object", "required": ["id", "customer_id", "customer_name", "transaction_type", "amount", "currency", "source_country", "destination_country", "monitoring_status", "risk_score", "suspicious_indicators", "requires_sar", "created_at"], "title": "TransactionResponse", "description": "Transaction response model."}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User email address"}, "full_name": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Full Name", "description": "Full name"}, "role": {"type": "string", "title": "Role", "description": "User role"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department", "description": "Department"}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "Initial password"}, "permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions", "description": "List of permission names", "default": []}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Account active status", "default": true}}, "type": "object", "required": ["email", "full_name", "role", "password"], "title": "UserCreate", "description": "User creation request model."}, "UserListResponse": {"properties": {"users": {"items": {"$ref": "#/components/schemas/UserResponse"}, "type": "array", "title": "Users"}, "total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "size": {"type": "integer", "title": "Size"}, "pages": {"type": "integer", "title": "Pages"}}, "type": "object", "required": ["users", "total", "page", "size", "pages"], "title": "UserListResponse", "description": "User list response model."}, "UserPermissionUpdate": {"properties": {"permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions", "description": "List of permission names to assign"}}, "type": "object", "required": ["permissions"], "title": "UserPermissionUpdate", "description": "User permission update request model."}, "UserResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "email": {"type": "string", "title": "Email"}, "full_name": {"type": "string", "title": "Full Name"}, "role": {"type": "string", "title": "Role"}, "department": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department"}, "tenant_id": {"type": "string", "title": "Tenant Id", "description": "Tenant identifier for multi-tenant data isolation.", "example": "tenant_bank_abc"}, "permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions"}, "is_active": {"type": "boolean", "title": "Is Active"}, "last_login": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Login"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "email", "full_name", "role", "department", "tenant_id", "permissions", "is_active", "last_login", "created_at", "updated_at"], "title": "UserResponse", "description": "User response model."}, "UserUpdate": {"properties": {"full_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 2}, {"type": "null"}], "title": "Full Name", "description": "Full name"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Role", "description": "User role"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department", "description": "Department"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Account active status"}}, "type": "object", "title": "UserUpdate", "description": "User update request model."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}, "JWTAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token authentication. Obtain token from /v1/auth/login endpoint."}, "TenantHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-Tenant-ID", "description": "Tenant isolation header. Required for all authenticated requests."}}, "examples": {"CustomerScreeningRequest": {"summary": "Screen customer against sanctions lists", "description": "Complete customer screening with PEP and sanctions checking", "value": {"customer_id": "CUST_001_2024", "customer_data": {"first_name": "<PERSON>", "last_name": "<PERSON>", "date_of_birth": "1985-03-15", "nationality": "US", "address": {"street": "123 Main Street", "city": "New York", "state": "NY", "country": "US", "postal_code": "10001"}, "identification": {"type": "passport", "number": "A12345678", "issuing_country": "US", "expiry_date": "2028-03-15"}}, "screening_options": {"include_sanctions": true, "include_pep": true, "include_adverse_media": true, "confidence_threshold": 0.85}}}, "TransactionMonitoringRequest": {"summary": "Monitor high-value wire transfer", "description": "Monitor international wire transfer for AML compliance", "value": {"transaction_id": "TXN_WR_20240115_001", "customer_id": "CUST_001_2024", "transaction_data": {"amount": 150000.0, "currency": "USD", "transaction_type": "wire_transfer", "transaction_date": "2024-01-15T14:30:00Z", "originator": {"name": "ABC Corporation", "account": "*********", "bank": "First National Bank", "country": "US"}, "beneficiary": {"name": "XYZ Trading Ltd", "account": "*********", "bank": "London Commercial Bank", "country": "GB"}, "purpose": "Trade settlement for commodity purchase"}, "monitoring_rules": {"check_sanctions": true, "check_threshold": true, "check_geography": true, "check_pattern": true}}}, "RegulatoryAnalysisRequest": {"summary": "Analyze new banking regulation", "description": "AI analysis of Federal Reserve regulation changes", "value": {"content": "The Federal Reserve Board announced final rules implementing revisions to the regulatory capital framework for banking organizations. The rules maintain the current capital requirements for community banking organizations with total consolidated assets of less than $10 billion. For larger banking organizations, the rules implement the final components of the Basel III regulatory capital reforms.", "analysis_type": "regulatory_impact", "options": {"model": "gpt-4", "include_entities": true, "include_recommendations": true, "confidence_threshold": 0.7}}}, "ComplianceTaskCreation": {"summary": "Create regulatory review task", "description": "Create a compliance task for regulatory change review", "value": {"title": "Review Basel III Capital Requirements Update", "description": "Analyze impact of new Basel III requirements on current capital allocation and reporting processes", "task_type": "regulatory_review", "priority": "high", "due_date": "2024-02-15T17:00:00Z", "assigned_to_user_id": "user_compliance_manager", "regulatory_reference": "Fed_Basel_III_2024_Q1", "workflow_id": "wf_regulatory_review_standard", "metadata": {"regulation_source": "Federal Reserve", "effective_date": "2024-06-01", "impact_assessment": "high", "business_units": ["risk_management", "capital_planning", "reporting"]}}}, "RiskAnalysisRequest": {"summary": "Comprehensive customer risk analysis", "description": "Multi-factor risk analysis for high-value customer", "value": {"analysis_type": "customer_risk_comprehensive", "customer_ids": ["CUST_001_2024", "CUST_002_2024"], "risk_factors": {"transaction_volume": true, "geographic_risk": true, "sanctions_exposure": true, "pep_status": true, "adverse_media": true, "business_relationship": true}, "analysis_period": {"start_date": "2023-07-01", "end_date": "2024-01-15"}, "options": {"include_recommendations": true, "generate_report": true, "alert_thresholds": {"high_risk": 0.8, "medium_risk": 0.5}}}}, "SuccessfulScreeningResponse": {"summary": "Successful customer screening result", "value": {"success": true, "data": {"screening_id": "SCR_20240115_001", "customer_id": "CUST_001_2024", "screening_date": "2024-01-15T14:30:00Z", "overall_risk_score": 0.25, "risk_level": "low", "screening_results": {"sanctions": {"matches_found": 0, "confidence": 0.0, "status": "clear"}, "pep": {"matches_found": 0, "confidence": 0.0, "status": "clear"}, "adverse_media": {"matches_found": 1, "confidence": 0.15, "status": "low_risk", "details": "Minor traffic violation 2019"}}, "recommendations": ["Customer cleared for standard service level", "Schedule routine review in 12 months"]}, "message": "Customer screening completed successfully", "timestamp": "2024-01-15T14:30:00Z", "request_id": "req_scr_123456"}}, "ComplianceMetricsResponse": {"summary": "Monthly compliance metrics", "value": {"success": true, "data": {"reporting_period": {"start_date": "2024-01-01", "end_date": "2024-01-31"}, "metrics": {"total_tasks": 156, "completed_tasks": 142, "completion_rate": 0.91, "average_completion_time_hours": 18.5, "overdue_tasks": 3, "escalated_tasks": 5}, "risk_metrics": {"high_risk_customers": 23, "medium_risk_customers": 145, "suspicious_transactions": 8, "sars_filed": 2}, "regulatory_updates": {"total_changes": 12, "high_impact": 2, "medium_impact": 6, "low_impact": 4}, "trends": {"month_over_month_completion": 0.05, "risk_score_trend": -0.02, "alert_volume_trend": 0.15}}, "message": "Compliance metrics retrieved successfully", "timestamp": "2024-02-01T09:00:00Z"}}, "ValidationErrorResponse": {"summary": "Validation error example", "value": {"success": false, "error": "VALIDATION_ERROR", "message": "Request validation failed", "details": {"field": "customer_data.date_of_birth", "issue": "Date format must be YYYY-MM-DD", "provided_value": "15/03/1985", "expected_format": "1985-03-15"}, "timestamp": "2024-01-15T14:30:00Z", "request_id": "req_err_123456"}}, "AuthenticationErrorResponse": {"summary": "Authentication error example", "value": {"success": false, "error": "AUTHENTICATION_ERROR", "message": "Invalid or expired JWT token", "details": {"error_code": "TOKEN_EXPIRED", "token_expiry": "2024-01-15T13:00:00Z", "current_time": "2024-01-15T14:30:00Z"}, "timestamp": "2024-01-15T14:30:00Z", "request_id": "req_auth_123456"}}}}, "security": [{"JWTAuth": [], "TenantHeader": []}]}